<?xml version="1.0" encoding="utf-8"?>
<resources>
    <declare-styleable name="UserIconView">
        <!--默认头像-->
        <attr name="default_image" format="reference" />
        <attr name="image_radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="SynthesizedImageView">
        <!--合成图片的背景-->
        <attr name="synthesized_image_bg" format="color" />
        <!--合成图片的默认图片-->
        <attr name="synthesized_default_image" format="reference" />
        <!--合成图片的尺寸-->
        <attr name="synthesized_image_size" format="dimension" />
        <!--多图片之间的空隙间隔-->
        <attr name="synthesized_image_gap" format="dimension" />
    </declare-styleable>

    <declare-styleable name="LineControllerView">
        <!-- 名称 -->
        <attr name="name" format="string"/>
        <!-- 内容或当前状态 -->
        <attr name="subject" format="string"/>
        <!-- 是否是列表中最后一个 -->
        <attr name="isBottom" format="boolean"/>
        <!-- 是否是列表中第一个 -->
        <attr name="isTop" format="boolean"/>
        <!-- 是否可以跳转 -->
        <attr name="canNav" format="boolean"/>
        <!-- 是否是开关 -->
        <attr name="isSwitch" format="boolean"/>
    </declare-styleable>

    <declare-styleable name="IndexBar">
        <attr name="indexBarTextSize" format="dimension"/>
        <attr name="indexBarPressBackground" format="color" />
    </declare-styleable>

    <declare-styleable name="core_round_rect_image_style">
        <attr name="round_radius" format="dimension" />
    </declare-styleable>

    <declare-styleable name="TitleBarLayout">
        <attr name="title_bar_middle_title" format="string" />
        <attr name="title_bar_can_return" format="boolean" />
    </declare-styleable>

    <declare-styleable name="RoundCornerImageView">
        <attr name="corner_radius" format="dimension" />
        <attr name="left_top_corner_radius" format="dimension" />
        <attr name="right_top_corner_radius" format="dimension" />
        <attr name="right_bottom_corner_radius" format="dimension" />
        <attr name="left_bottom_corner_radius" format="dimension" />
    </declare-styleable>

</resources>