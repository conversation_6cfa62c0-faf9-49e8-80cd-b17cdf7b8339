<?xml version="1.0" encoding="utf-8"?>
<resources>
    <string name="TUIKitErrorInProcess">Executing</string>
    <string name="TUIKitErrorInvalidParameters">Invalid parameter</string>
    <string name="TUIKitErrorIOOperateFaild">Local IO operation error</string>
    <string name="TUIKitErrorInvalidJson">Invalid JSON format</string>
    <string name="TUIKitErrorOutOfMemory">Out of storage</string>
    <string name="TUIKitErrorParseResponseFaild">PB parsing failed</string>
    <string name="TUIKitErrorSerializeReqFaild">PB serialization failed</string>
    <string name="TUIKitErrorSDKNotInit">IM SDK is not initialized.</string>
    <string name="TUIKitErrorLoadMsgFailed">Failed to load local database</string>
    <string name="TUIKitErrorDatabaseOperateFailed">Local database operation failed</string>
    <string name="TUIKitErrorCrossThread">Cross-thread error</string>
    <string name="TUIKitErrorTinyIdEmpty">User info is empty.</string>
    <string name="TUIKitErrorInvalidIdentifier">Invalid identifier</string>
    <string name="TUIKitErrorFileNotFound">File not found</string>
    <string name="TUIKitErrorFileTooLarge">File size exceeds the limit.</string>
    <string name="TUIKitErrorEmptyFile">Empty file</string>
    <string name="TUIKitErrorFileOpenFailed">Failed to open file</string>
    <string name="TUIKitErrorNotLogin">Not logged in to IM SDK</string>
    <string name="TUIKitErrorNoPreviousLogin">Not logged in to the user\'s account.</string>
    <string name="TUIKitErrorUserSigExpired">UserSig expired</string>
    <string name="TUIKitErrorLoginKickedOffByOther">Log in to the same account on other devices</string>
    <string name="TUIKitErrorTLSSDKInit">TLS SDK initialization failed</string>
    <string name="TUIKitErrorTLSSDKUninit">TLS SDK is not initialized.</string>
    <string name="TUIKitErrorTLSSDKTRANSPackageFormat">Invalid TLS SDK TRANS packet format</string>
    <string name="TUIKitErrorTLSDecrypt">TLS SDK decryption failed</string>
    <string name="TUIKitErrorTLSSDKRequest">TLS SDK request failed</string>
    <string name="TUIKitErrorTLSSDKRequestTimeout">TLS SDK request timed out</string>
    <string name="TUIKitErrorInvalidConveration">Invalid session</string>
    <string name="TUIKitErrorFileTransAuthFailed">Authentication failed during file transfer.</string>
    <string name="TUIKitErrorFileTransNoServer">Failed to get the server list via FTP.</string>
    <string name="TUIKitErrorFileTransUploadFailed">Failed to upload the file via FTP. Check your network connection.</string>
    <string name="TUIKitErrorFileTransDownloadFailed">Failed to download the file via FTP. Check whether your network is connected or the file or audio has expired.</string>
    <string name="TUIKitErrorHTTPRequestFailed">HTTP request failed</string>
    <string name="TUIKitErrorInvalidMsgElem">Invalid IM SDK message elem</string>
    <string name="TUIKitErrorInvalidSDKObject">Invalid object</string>
    <string name="TUIKitSDKMsgBodySizeLimit">Message length exceeds the limit.</string>
    <string name="TUIKitErrorSDKMsgKeyReqDifferRsp">Message key error</string>
    <string name="TUIKitErrorSDKGroupInvalidID">Invalid group ID. Custom group ID must be printable ASCII characters (0x20-0x7e), with maximum length of 48 bytes, and cannot be prefixed with @TGS#.</string>
    <string name="TUIKitErrorSDKGroupInvalidName">Group name is invalid, which cannot exceed 30 bytes.</string>
    <string name="TUIKitErrorSDKGroupInvalidIntroduction">Group description is invalid, which cannot exceed 240 bytes.</string>
    <string name="TUIKitErrorSDKGroupInvalidNotification">Group notice is invalid, which cannot exceed 300 bytes.</string>
    <string name="TUIKitErrorSDKGroupInvalidFaceURL">Group profile photo URL is invalid, which should not exceed 100 bytes.</string>
    <string name="TUIKitErrorSDKGroupInvalidNameCard">Group card is invalid, which cannot exceed 50 bytes.</string>
    <string name="TUIKitErrorSDKGroupMemberCountLimit">The maximum number of group members is exceeded.</string>
    <string name="TUIKitErrorSDKGroupJoinPrivateGroupDeny">Request to join private groups is not allowed.</string>
    <string name="TUIKitErrorSDKGroupInviteSuperDeny">Group owners cannot be invited.</string>
    <string name="TUIKitErrorSDKGroupInviteNoMember">The number of members to be invited cannot be 0.</string>
    <string name="TUIKitErrorSDKFriendShipInvalidProfileKey">Invalid data field</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddRemark">The remark field exceeds the limit of 96 bytes.</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddWording">The description field in the friend request is invalid, which should not exceed 120 bytes.</string>
    <string name="TUIKitErrorSDKFriendshipInvalidAddSource">The source field in the friend request is invalid, which should be prefixed with \"AddSource_Type_\".</string>
    <string name="TUIKitErrorSDKFriendshipFriendGroupEmpty">The friend list field is invalid. It is required with each list name of 30 bytes at most.</string>
    <string name="TUIKitErrorSDKNetEncodeFailed">Network link encryption failed</string>
    <string name="TUIKitErrorSDKNetDecodeFailed">Network link decryption failed</string>
    <string name="TUIKitErrorSDKNetAuthInvalid">Network link authentication not completed</string>
    <string name="TUIKitErrorSDKNetCompressFailed">Unable to compress data packet</string>
    <string name="TUIKitErrorSDKNetUncompressFaile">Packet decompression failed</string>
    <string name="TUIKitErrorSDKNetFreqLimit">Call frequency is limited, with up to 5 requests per second.</string>
    <string name="TUIKitErrorSDKnetReqCountLimit">Request queue is full. The number of concurrent requests exceeds the limit of 1000.</string>
    <string name="TUIKitErrorSDKNetDisconnect">Network disconnected. No connection is established, or no network is detected when a socket connection is established.</string>
    <string name="TUIKitErrorSDKNetAllreadyConn">Network connection has been established.</string>
    <string name="TUIKitErrorSDKNetConnTimeout">Network connection timed out. Try again once network connection is restored.</string>
    <string name="TUIKitErrorSDKNetConnRefuse">Network connection denied. Too many attempts. Service denied by the server.</string>
    <string name="TUIKitErrorSDKNetNetUnreach">No available route to the network. Try again once network connection is restored.</string>
    <string name="TUIKitErrorSDKNetSocketNoBuff">Call failed to due to insufficient buffer resources in the system. System busy. Internal error.</string>
    <string name="TUIKitERRORSDKNetResetByPeer">The peer resets the connection.</string>
    <string name="TUIKitErrorSDKNetSOcketInvalid">Invalid socket</string>
    <string name="TUIKitErrorSDKNetHostGetAddressFailed">IP address resolution failed</string>
    <string name="TUIKitErrorSDKNetConnectReset">Network is connected to an intermediate node or connection to the server is reset.</string>
    <string name="TUIKitErrorSDKNetWaitInQueueTimeout">Timed out waiting for request packet to enter the to-be-sent queue.</string>
    <string name="TUIKitErrorSDKNetWaitSendTimeout">Request packet has entered the to-be-sent queue. Timed out waiting to enter the network buffer of the system.</string>
    <string name="TUIKitErrorSDKNetWaitAckTimeut">Request packet has entered the network buffer of the system. Timed out waiting for response from server.</string>
    <string name="TUIKitErrorSDKSVRSSOConnectLimit">The number of Server connections exceeds the limit. Service denied by the server.</string>
    <string name="TUIKitErrorSDKSVRSSOVCode">Sending verification code timeout.</string>
    <string name="TUIKitErrorSVRSSOD2Expired">Key expired. Key is an internal bill generated according to usersig. The validity period of the key is less than or equal to the validity period of usersig. Please call timmanager again getInstance(). The login interface generates a new key.</string>
    <string name="TUIKitErrorSVRA2UpInvalid">Ticket expired. Ticket is an internal bill generated according to usersig. The validity period of ticket is less than or equal to that of usersig. Please call timmanager again getInstance(). The login interface generates a new ticket.</string>
    <string name="TUIKitErrorSVRA2DownInvalid">The bill failed verification or was hit by security. Please call timmanager again getInstance(). The login interface generates a new ticket.</string>
    <string name="TUIKitErrorSVRSSOEmpeyKey">Empty key is not allowed.</string>
    <string name="TUIKitErrorSVRSSOUinInvalid">The account in the key does not match the account in the request header.</string>
    <string name="TUIKitErrorSVRSSOVCodeTimeout">Timed out sending verification code.</string>
    <string name="TUIKitErrorSVRSSONoImeiAndA2">You need to bring your key and ticket.</string>
    <string name="TUIKitErrorSVRSSOCookieInvalid">Cookie check mismatch.</string>
    <string name="TUIKitErrorSVRSSODownTips">Send a prompt: Key expired.</string>
    <string name="TUIKitErrorSVRSSODisconnect">Link disconnected and screen locked.</string>
    <string name="TUIKitErrorSVRSSOIdentifierInvalid">Invalid identity.</string>
    <string name="TUIKitErrorSVRSSOClientClose">The device automatically logs out.</string>
    <string name="TUIKitErrorSVRSSOMSFSDKQuit">MSFSDK automatically logs out.</string>
    <string name="TUIKitErrorSVRSSOD2KeyWrong">the number of decryption failures exceeds the threshold, notify the terminal that it needs to be reset, please call timmanager again getInstance(). The login interface generates a new key.</string>
    <string name="TUIKitErrorSVRSSOUnsupport">Aggregation is not supported. A unified error code is returned to devices. The device stops aggregation on the persistent TCP connection.</string>
    <string name="TUIKitErrorSVRSSOPrepaidArrears">Prepaid service is in arrears.</string>
    <string name="TUIKitErrorSVRSSOPacketWrong">Invalid request packet format.</string>
    <string name="TUIKitErrorSVRSSOAppidBlackList">SDKAppID blocked list.</string>
    <string name="TUIKitErrorSVRSSOCmdBlackList">SDKAppID sets the service cmd blocked list.</string>
    <string name="TUIKitErrorSVRSSOAppidWithoutUsing">SDKAppID is disabled.</string>
    <string name="TUIKitErrorSVRSSOFreqLimit">Frequency limit (user), which is to limit the number of requests per second of a protocol.</string>
    <string name="TUIKitErrorSVRSSOOverload">Packet loss due to overload (system). Service denied by the connected server that failed to process too many requests.</string>
    <string name="TUIKitErrorSVRResNotFound">The resource file to be sent does not exist.</string>
    <string name="TUIKitErrorSVRResAccessDeny">Unable to access the resource file to be sent.</string>
    <string name="TUIKitErrorSVRResSizeLimit">File size exceeds the limit.</string>
    <string name="TUIKitErrorSVRResSendCancel">Sending is canceled by the user due to reasons like logging out when sending a message.</string>
    <string name="TUIKitErrorSVRResReadFailed">Failed to access the file content.</string>
    <string name="TUIKitErrorSVRResTransferTimeout">Timed out transferring the resource file.</string>
    <string name="TUIKitErrorSVRResInvalidParameters">Invalid parameter.</string>
    <string name="TUIKitErrorSVRResInvalidFileMd5">File MD5 verification failed.</string>
    <string name="TUIKitErrorSVRResInvalidPartMd5">Sharding MD5 verification failed.</string>
    <string name="TUIKitErrorSVRCommonInvalidHttpUrl">HTTP parsing error. Check the HTTP request URL format.</string>
    <string name="TUIKitErrorSVRCommomReqJsonParseFailed">JSON parsing error in the HTTP request. Check the JSON format.</string>
    <string name="TUIKitErrorSVRCommonInvalidAccount">The Identifier or UserSig in the request URI or JSON packet is incorrect.</string>
    <string name="TUIKitErrorSVRCommonInvalidSdkappid">Invalid SDKAppID. Check the SDKAppID validity.</string>
    <string name="TUIKitErrorSVRCommonRestFreqLimit">The REST API call frequency exceeds the limit. Reduce the request rate.</string>
    <string name="TUIKitErrorSVRCommonRequestTimeout">The service request timed out or the HTTP request format is incorrect. Check the error and try again.</string>
    <string name="TUIKitErrorSVRCommonInvalidRes">Requested resource error. Check the request URL.</string>
    <string name="TUIKitErrorSVRCommonIDNotAdmin">Fill in the Identifier field of the REST API request with the app admin\'s account.</string>
    <string name="TUIKitErrorSVRCommonSdkappidFreqLimit">SDKAppID request rate exceeds the limit. Reduce the request rate.</string>
    <string name="TUIKitErrorSVRCommonSdkappidMiss">SDKAppID is required for the REST API. Check the SDKAppID in the request URL.</string>
    <string name="TUIKitErrorSVRCommonRspJsonParseFailed">JSON parsing error in the HTTP response packet.</string>
    <string name="TUIKitErrorSVRCommonExchangeAccountTimeout">Account switching timed out.</string>
    <string name="TUIKitErrorSVRCommonInvalidIdFormat">The Identifier type of the request packet body is incorrect. Confirm that the Identifier is a string.</string>
    <string name="TUIKitErrorSVRCommonSDkappidForbidden">SDKAppID is disabled.</string>
    <string name="TUIKitErrorSVRCommonReqForbidden">Request is disabled.</string>
    <string name="TUIKitErrorSVRCommonReqFreqLimit">Too many requests. Try again later.</string>
    <string name="TUIKitErrorSVRCommonInvalidService">Your professional package has expired and been deactivated. Please log in to the im purchase page to re purchase the package. After purchase, it will take effect in 5 minutes.</string>
    <string name="TUIKitErrorSVRCommonSensitiveText">Text is filtered due to security reasons, which may contain sensitive words.</string>
    <string name="TUIKitErrorSVRCommonBodySizeLimit">The sending message package is too long. Currently, the maximum 8K message package length is supported. Please reduce the package size and try again.</string>
    <string name="TUIKitErrorSVRAccountUserSigExpired">UserSig has expired. Generate a new one.</string>
    <string name="TUIKitErrorSVRAccountUserSigEmpty">UserSig length is 0.</string>
    <string name="TUIKitErrorSVRAccountUserSigCheckFailed">UserSig verification failed.</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchPublicKey">Failed to verify UserSig with public key</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchId">The requested Identifier does not match the Identifier that is used to generate the UserSig.</string>
    <string name="TUIKitErrorSVRAccountUserSigMismatchSdkAppid">The requested SDKAppID does not match the SDKAppID of the generated UserSig.</string>
    <string name="TUIKitErrorSVRAccountUserSigPublicKeyNotFound">Public key does not exist when verifying UserSig.</string>
    <string name="TUIKitErrorSVRAccountUserSigSdkAppidNotFount">SDKAppID not found. Check the app information in the IM console.</string>
    <string name="TUIKitErrorSVRAccountInvalidUserSig">UserSig has expired. Generate a new one and try again.</string>
    <string name="TUIKitErrorSVRAccountNotFound">Requested user account not found.</string>
    <string name="TUIKitErrorSVRAccountSecRstr">Restricted for security reasons.</string>
    <string name="TUIKitErrorSVRAccountInternalTimeout">Internal server timeout. Try again.</string>
    <string name="TUIKitErrorSVRAccountInvalidCount">Invalid batch quantity in the request.</string>
    <string name="TUIkitErrorSVRAccountINvalidParameters">Invalid parameter. Check whether the fields are entered as required in the protocol.</string>
    <string name="TUIKitErrorSVRAccountAdminRequired">The request requires app admin permissions.</string>
    <string name="TUIKitErrorSVRAccountFreqLimit">Restricted due to too many failures and retries. Check if the UserSig is correct and try again after one minute.</string>
    <string name="TUIKitErrorSVRAccountBlackList">The account is added to the blocked list.</string>
    <string name="TUIKitErrorSVRAccountCountLimit">The number of accounts created exceeds that allowed in the free trial version. Upgrade to the professional version.</string>
    <string name="TUIKitErrorSVRAccountInternalError">Internal server error. Try again.</string>
    <string name="TUIKitErrorSVRProfileInvalidParameters">Request parameter error. Check if the request is correct according to the error message.</string>
    <string name="TUIKitErrorSVRProfileAccountMiss">Request parameter error. No user account specified to pull data.</string>
    <string name="TUIKitErrorSVRProfileAccountNotFound">Requested user account not found.</string>
    <string name="TUIKitErrorSVRProfileAdminRequired">The request requires app admin permissions.</string>
    <string name="TUIKitErrorSVRProfileSensitiveText">The data field contains sensitive words.</string>
    <string name="TUIKitErrorSVRProfileInternalError">Server internal error. Try again later.</string>
    <string name="TUIKitErrorSVRProfileReadWritePermissionRequired">You have no permission to read the data field. See the data field for details.</string>
    <string name="TUIKitErrorSVRProfileTagNotFound">The tag of the data field does not exist.</string>
    <string name="TUIKitErrorSVRProfileSizeLimit">The value of the data field exceeds 500 bytes.</string>
    <string name="TUIKitErrorSVRProfileValueError">The value of the standard data field is incorrect. See the standard data field for details.</string>
    <string name="TUIKitErrorSVRProfileInvalidValueFormat">The value type of the data field does not match. See the standard data field for details.</string>
    <string name="TUIKitErrorSVRFriendshipInvalidParameters">Request parameter error. Check if the request is correct according to the error message.</string>
    <string name="TUIKitErrorSVRFriendshipInvalidSdkAppid">SDKAppID does not match.</string>
    <string name="TUIKitErrorSVRFriendshipAccountNotFound">Requested user account not found.</string>
    <string name="TUIKitErrorSVRFriendshipAdminRequired">The request requires app admin permissions.</string>
    <string name="TUIKitErrorSVRFriendshipSensitiveText">The relation chain field contains sensitive words.</string>
    <string name="TUIKitErrorSVRFriendshipNetTimeout">Network timed out. Try again later.</string>
    <string name="TUIKitErrorSVRFriendshipWriteConflict">A write conflict occurred due to concurrent writes. The batch mode is recommended.</string>
    <string name="TUIKitErrorSVRFriendshipAddFriendDeny">The backend blocks the user from initiating friend requests.</string>
    <string name="TUIkitErrorSVRFriendshipCountLimit">The number of your friends exceeds the limit.</string>
    <string name="TUIKitErrorSVRFriendshipGroupCountLimit">The number of lists exceeds the limit.</string>
    <string name="TUIKitErrorSVRFriendshipPendencyLimit">You have reached the limit of pending friend requests.</string>
    <string name="TUIKitErrorSVRFriendshipBlacklistLimit">The number of accounts in the blocked list exceeds the limit.</string>
    <string name="TUIKitErrorSVRFriendshipPeerFriendLimit">The number of the other user\'s friends exceeds the limit.</string>
    <string name="TUIKitErrorSVRFriendshipInSelfBlacklist">You have blocked the other user. Unable to send friend request.</string>
    <string name="TUIKitErrorSVRFriendshipAllowTypeDenyAny">The other user\'s friend request verification mode is \"Decline friend request from any user\".</string>
    <string name="TUIKitErrorSVRFriendshipInPeerBlackList">You are blocked by the other user. Unable to send friend request.</string>
    <string name="TUIKitErrorSVRFriendshipAllowTypeNeedConfirm">Request sent. Wait for acceptance.</string>
    <string name="TUIKitErrorSVRFriendshipAddFriendSecRstr">The friend request was filtered by the security policy. Do not initiate friend requests too frequently.</string>
    <string name="TUIKitErrorSVRFriendshipPendencyNotFound">The pending friend request does not exist.</string>
    <string name="TUIKitErrorSVRFriendshipDelFriendSecRstr">The friend deletion request was filtered by the security policy. Do not initiate friend deletion requests too frequently.</string>
    <string name="TUIKirErrorSVRFriendAccountNotFoundEx">Requested user account not found.</string>
    <string name="TUIKitErrorSVRMsgPkgParseFailed">Failed to parse the request packet.</string>
    <string name="TUIKitErrorSVRMsgInternalAuthFailed">Internal authentication failed.</string>
    <string name="TUIKitErrorSVRMsgInvalidId">Invalid identifier</string>
    <string name="TUIKitErrorSVRMsgNetError">Network error. Try again.</string>
    <string name="TUIKitErrorSVRMsgPushDeny">A callback is triggered before sending a message in the private chat, and the App backend returns \"The message is prohibited from sending\".</string>
    <string name="TUIKitErrorSVRMsgInPeerBlackList">Unable to send messages in the private chat as you are blocked by the other user.</string>
    <string name="TUIKitErrorSVRMsgBothNotFriend">You are not a friend of this user. Unable to send messages.</string>
    <string name="TUIKitErrorSVRMsgNotPeerFriend">Unable to send messages in the private chat as you are not the other user\'s friend (one-way friend).</string>
    <string name="TUIkitErrorSVRMsgNotSelfFriend">Unable to send messages in the private chat as the other user is not your friend (one-way friend).</string>
    <string name="TUIKitErrorSVRMsgShutupDeny">Blocked from posting. Unable to send messages.</string>
    <string name="TUIKitErrorSVRMsgRevokeTimeLimit">Timed out recalling the message (default is 2 min).</string>
    <string name="TUIKitErrorSVRMsgDelRambleInternalError">An internal error occurred while deleting roaming messages.</string>
    <string name="TUIKitErrorSVRMsgJsonParseFailed">Failed to parse the JSON packet. Check whether the request packet meets the JSON specifications.</string>
    <string name="TUIKitErrorSVRMsgInvalidJsonBodyFormat">MsgBody in the JSON request packet does not conform to the message format description.</string>
    <string name="TUIKitErrorSVRMsgInvalidToAccount">The To_Account field is missing from the JSON request packet body or the type of the To_Account field is not Integer.</string>
    <string name="TUIKitErrorSVRMsgInvalidRand">The MsgRandom field is missing from the JSON request packet body or the type of the MsgRandom field is not Integer.</string>
    <string name="TUIKitErrorSVRMsgInvalidTimestamp">The MsgTimeStamp field is missing from the JSON request packet body or the type of the MsgTimeStamp field is not Integer.</string>
    <string name="TUIKitErrorSVRMsgBodyNotArray">The MsgBody type in the JSON request packet body is not Array.</string>
    <string name="TUIKitErrorSVRMsgInvalidJsonFormat">The JSON request packet does not conform to the message format description.</string>
    <string name="TUIKitErrorSVRMsgToAccountCountLimit">The number of accounts to which messages are sent in batch exceeds 500.</string>
    <string name="TUIKitErrorSVRMsgToAccountNotFound">To_Account is not registered or does not exist.</string>
    <string name="TUIKitErrorSVRMsgTimeLimit">Invalid offline message storage time (up to 7 days).</string>
    <string name="TUIKitErrorSVRMsgInvalidSyncOtherMachine">The type of the SyncOtherMachine field in the JSON request packet body is not Integer.</string>
    <string name="TUIkitErrorSVRMsgInvalidMsgLifeTime">The type of the MsgLifeTime field in the JSON request packet body is not Integer.</string>
    <string name="TUIKitErrorSVRMsgBodySizeLimit">The length of JSON data packet exceeds the limit. The message packet body shall not exceed 8 KB.</string>
    <string name="TUIKitErrorSVRmsgLongPollingCountLimit">Forced logout on the Web page during long polling (the number of online instances on the Web page exceeds the limit).</string>
    <string name="TUIKitErrorSVRGroupApiNameError">The API name in the request is incorrect.</string>
    <string name="TUIKitErrorSVRGroupAccountCountLimit">The number of accounts in the request packet body exceeds the limit.</string>
    <string name="TUIkitErrorSVRGroupFreqLimit">Call frequency is limited. Reduce the call frequency.</string>
    <string name="TUIKitErrorSVRGroupPermissionDeny">Insufficient operation permissions</string>
    <string name="TUIKitErrorSVRGroupInvalidReq">Invalid request</string>
    <string name="TUIKitErrorSVRGroupSuperNotAllowQuit">Group owner is not allowed to leave this group.</string>
    <string name="TUIKitErrorSVRGroupNotFound">The group does not exist.</string>
    <string name="TUIKitErrorSVRGroupJsonParseFailed">Failed to parse the JSON packet. Check whether the packet body conforms to the JSON format.</string>
    <string name="TUIKitErrorSVRGroupInvalidId">The identifier that is used to initiated the operation is invalid. Check whether the identifier of the user who initiated the operation is correct.</string>
    <string name="TUIKitErrorSVRGroupAllreadyMember">The invited user is a group member.</string>
    <string name="TUIKitErrorSVRGroupFullMemberCount">The number of group members has reached the limit. Unable to add the user in the request to the group.</string>
    <string name="TUIKitErrorSVRGroupInvalidGroupId">Invalid group ID. Check whether the group ID is correct.</string>
    <string name="TUIKitErrorSVRGroupRejectFromThirdParty">The app backend rejected this operation via a third-party callback.</string>
    <string name="TUIKitErrorSVRGroupShutDeny">This user is blocked from posting and thus cannot send messages. Check whether the sender is blocked from posting.</string>
    <string name="TUIKitErrorSVRGroupRspSizeLimit">The response packet length exceeds the limit.</string>
    <string name="TUIKitErrorSVRGroupAccountNotFound">Requested user account not found.</string>
    <string name="TUIKitErrorSVRGroupGroupIdInUse">The group ID has been used. Select another one.</string>
    <string name="TUIKitErrorSVRGroupSendMsgFreqLimit">The frequency of sending messages exceeds the limit. Extend the interval between sending messages.</string>
    <string name="TUIKitErrorSVRGroupReqAllreadyBeenProcessed">This invitation or request has been processed.</string>
    <string name="TUIKitErrorSVRGroupGroupIdUserdForSuper">The group ID has been used by the group owner. It can be used directly.</string>
    <string name="TUIKitErrorSVRGroupSDkAppidDeny">The command word used in the SDKAppID request has been disabled.</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgNotFound">This message does not exist.</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgTimeLimit">Timed out recalling the message (default is 2 min).</string>
    <string name="TUIKitErrorSVRGroupRevokeMsgDeny">Unable to recall this message.</string>
    <string name="TUIKitErrorSVRGroupNotAllowRevokeMsg">Unable to recall messages in groups of this type.</string>
    <string name="TUIKitErrorSVRGroupRemoveMsgDeny">Unable to delete messages of this type.</string>
    <string name="TUIKitErrorSVRGroupNotAllowRemoveMsg">Unable to delete messages in the voice/video chat room and the broadcast group of online members.</string>
    <string name="TUIKitErrorSVRGroupAvchatRoomCountLimit">The number of created voice/video chat rooms exceeds the limit.</string>
    <string name="TUIKitErrorSVRGroupCountLimit">The number of groups that a single user can create and join exceeds the limit.</string>
    <string name="TUIKitErrorSVRGroupMemberCountLimit">The number of group members exceeds the limit.</string>
    <string name="TUIKitErrorSVRNoSuccessResult">No success result returned for the batch operation.</string>
    <string name="TUIKitErrorSVRToUserInvalid">IM: Invalid recipient</string>
    <string name="TUIKitErrorSVRRequestTimeout">Request timeout</string>
    <string name="TUIKitErrorSVRInitCoreFail">INIT CORE module failed</string>
    <string name="TUIKitErrorExpiredSessionNode">SessionNode is null.</string>
    <string name="TUIKitErrorLoggedOutBeforeLoginFinished">Logged out before login (returned at login time)</string>
    <string name="TUIKitErrorTLSSDKNotInitialized">tlssdk is not initialized.</string>
    <string name="TUIKitErrorTLSSDKUserNotFound">The user information for TLSSDK was not found.</string>
    <string name="TUIKitErrorBindFaildRegTimeout">Registration timed out</string>
    <string name="TUIKitErrorBindFaildIsBinding">The bind operation in progress.</string>
    <string name="TUIKitErrorPacketFailUnknown">Unknown error occurred while sending packet</string>
    <string name="TUIKitErrorPacketFailReqNoNet">No network connection when sending request packet, which is converted to case ERR_REQ_NO_NET_ON_REQ:</string>
    <string name="TUIKitErrorPacketFailRespNoNet">No network connection when sending response packet, which is converted to case ERR_REQ_NO_NET_ON_RSP:</string>
    <string name="TUIKitErrorPacketFailReqNoAuth">No permission when sending request packet</string>
    <string name="TUIKitErrorPacketFailSSOErr">SSO error</string>
    <string name="TUIKitErrorPacketFailRespTimeout">Response timed out</string>
    <string name="TUIKitErrorFriendshipProxySyncing">proxy_manager failed to sync SVR data</string>
    <string name="TUIKitErrorFriendshipProxySyncedFail">proxy_manager sync failed</string>
    <string name="TUIKitErrorFriendshipProxyLocalCheckErr">proxy_manager request parameter is invalid in local check.</string>
    <string name="TUIKitErrorGroupInvalidField">group assistant request field contains non-preset fields.</string>
    <string name="TUIKitErrorGroupStoreageDisabled">Local storage of group assistant group data is disabled.</string>
    <string name="TUIKitErrorLoadGrpInfoFailed">Failed to load groupinfo from storage</string>
    <string name="TUIKitErrorReqNoNetOnReq">No network connection when sending request</string>
    <string name="TUIKitErrorReqNoNetOnResp">No network connection when sending response</string>
    <string name="TUIKitErrorServiceNotReady">QALSDK service is not ready.</string>
    <string name="TUIKitErrorLoginAuthFailed">Account verification failed (user info get failed)</string>
    <string name="TUIKitErrorNeverConnectAfterLaunch">Failed to connect network when the app is lunched.</string>
    <string name="TUIKitErrorReqFailed">QAL execution failed</string>
    <string name="TUIKitErrorReqInvaidReq">Invalid request. Invalid toMsgService.</string>
    <string name="TUIKitErrorReqOnverLoaded">Request queue is full.</string>
    <string name="TUIKitErrorReqKickOff">Forced logout on another device</string>
    <string name="TUIKitErrorReqServiceSuspend">Service suspended</string>
    <string name="TUIKitErrorReqInvalidSign">SSO signature error</string>
    <string name="TUIKitErrorReqInvalidCookie">Invalid SSO cookie</string>
    <string name="TUIKitErrorLoginTlsRspParseFailed">TSL response packet is verified at login time. Packet length error.</string>
    <string name="TUIKitErrorLoginOpenMsgTimeout">Timeout occurred when OPENSTATSVC attempted to report status to OPENMSG during login.</string>
    <string name="TUIKitErrorLoginOpenMsgRspParseFailed">Response parsing failed when OPENSTATSVC reports status to OPENMSG during login.</string>
    <string name="TUIKitErrorLoginTslDecryptFailed">TLS decryption failed at login time.</string>
    <string name="TUIKitErrorWifiNeedAuth">Verification is required for Wi-Fi connection.</string>
    <string name="TUIKitErrorUserCanceled">Canceled by user</string>
    <string name="TUIkitErrorRevokeTimeLimitExceed">Timed out recalling the message (default is 2 min).</string>
    <string name="TUIKitErrorLackUGExt">Missing UGC extension pack</string>
    <string name="TUIKitErrorAutoLoginNeedUserSig">Local ticket for auto login expired. userSig is required for manual login.</string>
    <string name="TUIKitErrorQALNoShortConneAvailable">No available SSO for short connections.</string>
    <string name="TUIKitErrorReqContentAttach">Message content is filtered due to security reasons.</string>
    <string name="TUIKitErrorLoginSigExpire">Returned at login time. Ticket expired.</string>
    <string name="TUIKitErrorSDKHadInit">SDK has been initialized. Do not initialize again.</string>
    <string name="TUIKitErrorOpenBDHBase">Openbdh error</string>
    <string name="TUIKitErrorRequestNoNetOnReq">No network connection when sending request. Try again once network connection is restored.</string>
    <string name="TUIKitErrorRequestNoNetOnRsp">No network connection when sending response. Try again once network connection is restored.</string>
    <string name="TUIKitErrorRequestOnverLoaded">Request queue is full.</string>
    <string name="TUIKitErrorEnableUserStatusOnConsole">The user status not supported. Please enable the ability in the console first.</string>
</resources>