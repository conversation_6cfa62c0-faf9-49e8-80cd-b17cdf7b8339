<?xml version="1.0" encoding="utf-8"?>
<resources>
    <style name="TUIKit_AlertDialogStyle" parent="@android:style/Theme.Dialog">
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:background">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsFloating">true</item>
        <item name="android:windowFrame">@null</item>
        <item name="android:backgroundDimEnabled">true</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">true</item>
    </style>

    <style name="CoreActivityTranslucent">
        <item name="android:background">@android:color/transparent</item>
        <item name="android:colorBackgroundCacheHint">@null</item>
        <item name="android:windowActionBar">false</item>
        <item name="android:windowNoTitle">true</item>
        <item name="android:statusBarColor">@android:color/transparent</item>
        <item name="android:backgroundDimEnabled">false</item>
        <item name="android:windowBackground">@android:color/transparent</item>
        <item name="android:windowContentOverlay">@null</item>
        <item name="android:windowIsTranslucent">true</item>
        <item name="android:windowAnimationStyle">@null</item>
        <item name="android:windowDisablePreview">true</item>
    </style>

    <style name="PopupInputCardAnim">
        <item name="android:windowEnterAnimation">@anim/core_popup_in_anim</item>
        <item name="android:windowExitAnimation">@anim/core_popup_out_anim</item>
    </style>

    <style name="TUIBaseTheme">
        <item name="user_status_offline">@drawable/core_icon_offline_status</item>
    </style>

</resources>