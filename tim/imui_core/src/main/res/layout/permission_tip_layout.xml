<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content"
    android:background="@drawable/core_permission_dialog_bg"
    android:clickable="true"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tips_title"
        android:textSize="15sp"
        android:textStyle="bold"
        android:gravity="center"
        android:textColor="@color/black"
        android:text="@string/core_permission_dialog_title"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginTop="20dp"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"/>

    <TextView
        android:id="@+id/tips"
        android:layout_marginTop="12dp"
        android:layout_marginStart="30dp"
        android:layout_marginEnd="30dp"
        android:layout_marginBottom="20dp"
        android:textSize="15sp"
        android:gravity="center"
        android:textColor="#888888"
        android:layout_width="match_parent"
        android:layout_height="wrap_content" />

    <View
        android:background="#EEEEEE"
        android:layout_width="match_parent"
        android:layout_height="1dp" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/negative_btn"
            android:layout_marginTop="15dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="15dp"
            android:gravity="center"
            android:textColor="@color/black"
            android:textStyle="bold"
            android:textSize="15.55sp"
            android:text="@string/cancel"
            android:layout_weight="0.5"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

        <View
            android:background="#EEEEEE"
            android:layout_width="0.5dp"
            android:layout_height="match_parent"/>

        <TextView
            android:id="@+id/positive_btn"
            android:layout_marginTop="15dp"
            android:layout_marginStart="20dp"
            android:layout_marginEnd="20dp"
            android:layout_marginBottom="15dp"
            android:text="@string/core_permission_dialog_positive_setting_text"
            android:textSize="15.55sp"
            android:textStyle="bold"
            android:layout_weight="0.5"
            android:gravity="center"
            android:layout_width="match_parent"
            android:layout_height="wrap_content" />

    </LinearLayout>
</LinearLayout>