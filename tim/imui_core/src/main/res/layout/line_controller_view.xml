<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:orientation="vertical">

    <View
        android:id="@+id/top_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line" />

    <RelativeLayout
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@color/white">

        <TextView
            android:id="@+id/name"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_alignParentLeft="true"
            android:layout_marginLeft="@dimen/core_line_controller_margin_left"
            android:layout_centerVertical="true"
            android:textColor="@color/core_line_controller_title_color"
            android:textSize="@dimen/core_line_controller_text_size" />

        <RelativeLayout
            android:id="@+id/contentText"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginLeft="20dp"
            android:layout_centerVertical="true"
            android:layout_toLeftOf="@id/btnSwitch"
            android:layout_toRightOf="@+id/name">

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginLeft="20dp"
                android:layout_marginRight="20dp"
                android:layout_centerVertical="true"
                android:layout_alignParentRight="true"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/content"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:ellipsize="end"
                    android:singleLine="true"
                    android:gravity="right"
                    android:textIsSelectable="true"
                    android:textColor="@color/core_line_controller_content_color"
                    android:textSize="@dimen/core_line_controller_text_size" />

                <ImageView
                    android:id="@+id/rightArrow"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_gravity="center"
                    android:layout_marginLeft="10.56dp"
                    android:src="@drawable/arrow_right" />
            </LinearLayout>
        </RelativeLayout>

        <Switch
            android:id="@+id/btnSwitch"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:thumb="@drawable/switch_thumb"
            android:track="@drawable/switch_track"
            android:switchMinWidth="@dimen/switch_mini_width"
            android:layout_alignParentEnd="true"
            android:layout_centerVertical="true"
            android:textOff=""
            android:textOn=""
            android:layout_marginRight="15dp" />

        <View
            android:id="@+id/disable_mask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:visibility="gone"
            android:background="@color/core_line_controller_white_translucent_color"/>
    </RelativeLayout>

    <View
        android:id="@+id/bottom_line"
        android:layout_width="match_parent"
        android:layout_height="1px"
        android:background="@color/line" />
</LinearLayout>