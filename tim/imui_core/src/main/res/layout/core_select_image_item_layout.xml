<?xml version="1.0" encoding="utf-8"?>
<RelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:id="@+id/item_content"
    android:layout_width="wrap_content"
    android:layout_height="wrap_content">

    <com.tencent.qcloud.tuicore.component.RoundCornerImageView
        android:id="@+id/content_image"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:scaleType="centerCrop"
        app:corner_radius="8dp" />

    <RelativeLayout
        android:id="@+id/selected_border_area"
        android:layout_alignParentTop="true"
        android:layout_alignParentLeft="true"
        android:layout_alignParentBottom="true"
        android:layout_alignParentRight="true"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_alignParentStart="true"
        android:layout_alignParentEnd="true">

        <com.tencent.qcloud.tuicore.component.RoundCornerImageView
            android:id="@+id/select_border"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@drawable/selected_border"
            android:scaleType="centerCrop"
            app:corner_radius="8dp" />

        <ImageView
            android:layout_width="21dp"
            android:layout_height="21dp"
            android:layout_alignTop="@id/select_border"
            android:layout_alignEnd="@id/select_border"
            android:layout_alignRight="@id/select_border"
            android:layout_margin="4dp"
            android:background="?attr/core_selected_icon"
            android:visibility="visible" />
    </RelativeLayout>
</RelativeLayout>

