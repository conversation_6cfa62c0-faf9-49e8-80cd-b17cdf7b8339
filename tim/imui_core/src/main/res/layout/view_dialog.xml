<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:id="@+id/ll_background"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:background="@drawable/alert_bg"
    android:orientation="vertical">

    <TextView
        android:id="@+id/tv_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginLeft="15dp"
        android:layout_marginRight="15dp"
        android:layout_marginTop="15dp"
        android:gravity="center"
        android:clickable="true"
        android:focusableInTouchMode="true"
        android:textColor="@color/black"
        android:textSize="16sp"
        android:textStyle="bold"
        android:focusable="true" />


    <LinearLayout
        android:id="@+id/ll_alert"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical" />

    <ImageView
        android:layout_width="match_parent"
        android:layout_height="0.5dp"
        android:layout_marginTop="10dp"
        android:background="@color/dialog_line_bg" />

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal">

        <Button
            android:id="@+id/btn_neg"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/trans_bg"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="?attr/core_primary_color"
            android:textSize="16sp" />

        <ImageView
            android:id="@+id/img_line"
            android:layout_width="0.5dp"
            android:layout_height="50dp"
            android:background="@color/dialog_line_bg" />

        <Button
            android:id="@+id/btn_pos"
            android:layout_width="wrap_content"
            android:layout_height="50dp"
            android:layout_weight="1"
            android:background="@drawable/trans_bg"
            android:gravity="center"
            android:textAllCaps="false"
            android:textColor="?attr/core_primary_color"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>