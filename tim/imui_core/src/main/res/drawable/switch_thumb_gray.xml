<?xml version="1.0" encoding="utf-8"?>
<selector xmlns:android="http://schemas.android.com/apk/res/android">
    <item>
        <layer-list>
            <item android:bottom="@dimen/switch_thumb_padding" android:left="@dimen/switch_thumb_padding" android:right="@dimen/switch_thumb_padding" android:top="@dimen/switch_thumb_padding">
                <shape android:shape="oval">
                    <size android:width="@dimen/switch_thumb_width" android:height="@dimen/switch_thumb_height" />
                    <gradient android:endColor="#ffffff" android:startColor="#ffffff" />
                    <stroke
                        android:width="0.7dp"
                        android:color="#d2d2d2"/>
                </shape>
            </item>
        </layer-list>
    </item>
</selector>