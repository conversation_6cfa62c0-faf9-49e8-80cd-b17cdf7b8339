<?xml version="1.0" encoding="utf-8"?>
<manifest xmlns:android="http://schemas.android.com/apk/res/android">

    <application>
        <activity
            android:name=".component.activities.SelectionActivity"
            android:screenOrientation="portrait" />
        <activity
            android:name=".component.activities.ImageSelectActivity"
            android:screenOrientation="portrait" />

        <activity
            android:name=".util.PermissionRequester$PermissionActivity"
            android:configChanges="orientation|keyboardHidden|screenSize"
            android:multiprocess="true"
            android:launchMode="singleTask"
            android:theme="@style/CoreActivityTranslucent"
            android:windowSoftInputMode="stateHidden|stateAlwaysHidden" />

        <provider
            android:name=".ServiceInitializer"
            android:authorities="${applicationId}.TUICore.Init"
            android:enabled="true"
            android:exported="false"/>

        <provider
            android:name=".util.TUIFileProvider"
            android:authorities="${applicationId}.tuicore.fileprovider"
            android:exported="false"
            android:grantUriPermissions="true">
            <meta-data
                android:name="android.support.FILE_PROVIDER_PATHS"
                android:resource="@xml/file_paths_public"/>
        </provider>
    </application>
</manifest>