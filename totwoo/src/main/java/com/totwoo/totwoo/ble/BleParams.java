package com.totwoo.totwoo.ble;

import android.content.Context;
import android.text.TextUtils;

import androidx.annotation.Nullable;

import com.totwoo.library.exception.DbException;
import com.totwoo.totwoo.R;
import com.totwoo.totwoo.ToTwooApplication;
import com.totwoo.totwoo.bean.CustomOrderBean;
import com.totwoo.totwoo.utils.CustomOrderDbHelper;
import com.totwoo.totwoo.utils.PreferencesUtils;

import java.util.ArrayList;

/**
 * Created by totwoo on 2018/6/1.
 */

public class BleParams {
    /**
     * 硬件类型: 默认BLE
     */
    public static final int JEWELRY_HARDWARE_TYPE_BLE = 0;
    /**
     * 硬件类型: NFC
     */
    public static final int JEWELRY_HARDWARE_TYPE_NFC = 1;

    public static final String ACTION_RECONNECT = "com_totwoo_towooo_jewconnect_service_reconnect";
    public static final String ACTION_KEEP_ALIVE = "jewconnect_service_keep_alive";
    public static final String ACTION_START = "com_totwoo_towooo_jewconnect_service_start";
    public static final String ACTION_REFRESH_FOREGROUND_STATUS = "com_totwoo_towooo_jewconnect_service_refresh_foreground_status";
    /**
     * 后台扫描到已配对的首饰, 唤醒时的 ACTION
     */
    public static final String ACTION_BLE_SCAN_NOTIFY = "com_totwoo_towooo_jewconnect_service_scan_notify";

    public static final String ACTION_SEND_TOTWOO = "com_totwoo_send_totwoo";

    public final static String PAIRED_FIRST_CONNECT = "paired_first_connect";
    public final static String PAIRED_CHANGE_CONNECT = "PAIRED_CHANGE_CONNECT";
    public final static String PAIRED_JEWELRY_NAME_TAG = "paired_jewelry_name";
    public final static String PAIRED_JEWELRY_INFO_ADDED = "paired_jewelry_info_added";
    public final static String SELECT_VIBRATION_INDEX_TAG = "select_vibration_index_tag";
    public final static String PAIRED_BLE_ADRESS_TAG = "paired_ble_adress";
    public static final String TOTWOO_DEVICE_INFO = "totwoo_device_info";
    public final static String EXTRA_BLE_DATA_TAG_FIRMWARE_REVISION = "extra_ble_data_tag_firmware_ver";
    public final static String SAFE_JEWLERY_IMEI = "safe_jewlery_imei";
    public final static String EXTRA_BLE_DATA_TAG_SYSTEM_ID = "extra_ble_data_tag_system_id";
    // 电量提醒-50
    public static final String QUANTITYREMINDER_50 = "Quantity_Reminder_50";
    // 电量提醒-20
    public static final String QUANTITYREMINDER_20 = "Quantity_Reminder_20";
    // 电量提醒-5
    public static final String QUANTITYREMINDER_5 = "Quantity_Reminder_5";
//    public static final String LASTQUANTITY = "Jewelry_Quantity_Percent";

    // 硬件OTA模式的, 硬件名称
    public static final String OTA_DEVICES_NAME = "PE829U";

    public static final String OTA_DEVICES_NAME_NEW = "PE830U";

    public static final String OTA_DEVICES_NAME_BANK = "PE831U";
    public static final String OTA_DEVICES_NAME_SAFE = "PE832U";

    public static final String COMMON_JEWELEY_PRE = "TWO";
    /**
     * 珠宝类型: 绽放 吊坠
     */
    public static final String JEWELRY_BLE_NAME_MP = "TWO01";
    public static final String JEWELRY_BLE_NAME_MP_NEW = "TWO36";
    /**
     * 珠宝类型: 绽放 手镯
     */
    public static final String JEWELRY_BLE_NAME_MB = "TWO02";
    /**
     * 珠宝类型: 勇敢 吊坠
     */
    public static final String JEWELRY_BLE_NAME_DP = "TWO03";
    public static final String JEWELRY_BLE_NAME_DP_NEW = "TWO21";
    /**
     * 珠宝类型: 勇敢 手镯
     */
    public static final String JEWELRY_BLE_NAME_DB = "TWO04";
    public static final String JEWELRY_BLE_NAME_DB_NEW = "TWO22";
    /**
     * 珠宝类型：篮球 吊坠
     */
    public static final String JEWELRY_BLE_NAME_BP = "TWO05";
    /**
     * 珠宝类型：篮球 手镯
     */
    public static final String JEWELRY_BLE_NAME_BB = "TWO06";
    /**
     * 珠宝类型：金狮 吊坠
     */
    public static final String JEWELRY_BLE_NAME_GP = "TWO07";
    /**
     * 珠宝类型：金狮 手镯
     */
    public static final String JEWELRY_BLE_NAME_GB = "TWO08";
    /**
     * 珠宝类型：时光记忆 吊坠
     */
    public static final String JEWELRY_BLE_NAME_MEMORYP = "TWO09";
    public static final String JEWELRY_BLE_NAME_MEMORY_NP = "TWO37";
    /**
     * 珠宝类型：银行项目 智爱系列 吊坠
     */
    public static final String JEWELRY_BLE_NAME_SL = "TWO10";
    /**
     * 珠宝类型：机芯（只有英文版有）
     */
    public static final String JEWELRY_BLE_NAME_SH = "TWO11";
    /**
     * 珠宝类型：流星花园绽放手镯（只有中文版有）
     */
    public static final String JEWELRY_BLE_NAME_MGB = "TWO32";
    /**
     * 珠宝类型：勇敢皮绳手镯
     */
    public static final String JEWELRY_BLE_NAME_WBCB = "TWO20";
    public static final String JEWELRY_BLE_NAME_WBCB_NEW = "TWO23";

    /**
     * 珠宝类型：三叶草 吊坠
     * smart clover
     */
    public static final String JEWELRY_BLE_NAME_SC = "TWO24";

    /**
     * 珠宝类型：星梦 吊坠
     * star dream
     */
    public static final String JEWELRY_BLE_NAME_SDP = "TWO25";
    /**
     * 珠宝类型：星梦 手镯
     * star dream
     */
    public static final String JEWELRY_BLE_NAME_SDB = "TWO26";

    /**
     * 彩色玻璃首饰
     * 中文名称：教堂花窗 - 吊坠
     * 英文名称：Wonderland - Pendant
     */
    public static final String JEWELRY_BLE_NAME_WL = "TWO31";

    /**
     * 珠宝类型：贝母片
     */
    public static final String JEWELRY_BLE_NAME_MWP = "TWO33";//皓月系列 手链
    public static final String JEWELRY_BLE_NAME_MWE = "TWO34";//项链
    public static final String JEWELRY_BLE_NAME_MWR = "TWO35";

    /**
     * Security Apple 安全挂坠
     */
    public static final String JEWELRY_BLE_NAME_SA = "TWO40";
    public static final String JEWELRY_BLE_NAME_SAL = "TWO41";
    public static final String JEWELRY_BLE_NAME_SA2 = "TWO42";
    public static final String JEWELRY_BLE_NAME_SA3 = "TWO43";
    /**
     * code - pendant  密语首饰
     */
    public static final String JEWELRY_BLE_NAME_GFP = "TWO51";
    public static final String JEWELRY_BLE_NAME_GFB = "TWO52";
    public static final String JEWELRY_BLE_NAME_GMP = "TWO53";
    public static final String JEWELRY_BLE_NAME_GMB = "TWO54";
    /**
     * love - letter  情书首饰
     */
    public static final String JEWELRY_BLE_NAME_LLF = "TWO61";
    public static final String JEWELRY_BLE_NAME_LLM = "TWO62";

    public static final String JEWELRY_BLE_NAME_KF = "TWO63";
    public static final String JEWELRY_BLE_NAME_KM = "TWO64";

    public static final String JEWELRY_BLE_NAME_UF = "TWO65";
    public static final String JEWELRY_BLE_NAME_UM = "TWO66";
    //没定名字，为未来的NB11预留不能音乐闪光的固件名称
    public static final String JEWELRY_BLE_NAME_LL7 = "TWO67";
    public static final String JEWELRY_BLE_NAME_LL8 = "TWO68";
    public static final String JEWELRY_BLE_NAME_LL9 = "TWO69";

    public static final String JEWELRY_BLE_NAME_EDBF = "TWO71";
    public static final String JEWELRY_BLE_NAME_EMBF = "TWO72";
    public static final String JEWELRY_BLE_NAME_EDBM = "TWO73";
    public static final String JEWELRY_BLE_NAME_EMBM = "TWO74";
    public static final String JEWELRY_BLE_NAME_EMBMN = "TWO75";
    public static final String JEWELRY_BLE_NAME_80 = "TWO80";
    public static final String JEWELRY_BLE_NAME_81 = "TWO81";

    public static final String JEWELRY_BLE_NAME_82 = "TWO82";

    public static final String JEWELRY_BLE_NAME_82_01 = "TWO82-01";
    public static final String JEWELRY_BLE_NAME_83 = "TWO83";
    public static final String JEWELRY_BLE_NAME_84 = "TWO84";
    public static final String JEWELRY_BLE_NAME_85 = "TWO85";
    public static final String JEWELRY_BLE_NAME_86 = "TWO86";
    public static final String JEWELRY_BLE_NAME_87 = "TWO87";
    public static final String JEWELRY_BLE_NAME_88 = "TWO88";
    public static final String JEWELRY_BLE_NAME_89 = "TWO89";

    /*长条*/
    public static final String JEWELRY_BLE_NAME_90 = "TWO90";

    /**
     * 珠宝类型：棒棒糖 吊坠
     */
    public static final String JEWELRY_BLE_NAME_LPP = "TWO55";

    /**
     * NFC 首饰, 名称前缀
     */
    public static final String JEWELRY_NFC_NAME_PREF = "T_";

    /**
     * NFC 首饰 - MEET系列
     */
    public static final String JEWELRY_NFC_NAME_MEET_PREF = JEWELRY_NFC_NAME_PREF + "MEET_";

    public static final String JEWELRY_NFC_NAME_MEET_001 = JEWELRY_NFC_NAME_MEET_PREF + "001";
    public static final String JEWELRY_NFC_NAME_MEET_002 = JEWELRY_NFC_NAME_MEET_PREF + "002";


    public static int getJewelryResourceId(String jewName) {
        int jewelryResourceId;
        if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_MP_NEW)) {
            jewelryResourceId = R.drawable.product_jewelry_name_bmp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MB)) {
            jewelryResourceId = R.drawable.product_jewelry_name_bmb;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_DP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_DP_NEW)) {
            jewelryResourceId = R.drawable.product_jewelry_name_bdp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_DB) || jewName.equals(BleParams.JEWELRY_BLE_NAME_DB_NEW)) {
            jewelryResourceId = R.drawable.product_jewelry_name_mdb;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_BP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_bbp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_BB)) {
            jewelryResourceId = R.drawable.product_jewelry_name_bbb;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_lp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GB)) {
            jewelryResourceId = R.drawable.product_jewelry_name_lb;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORY_NP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_mm;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
            jewelryResourceId = R.drawable.product_jewelry_name_sl;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SH)) {
            jewelryResourceId = R.drawable.product_jewelry_name_sh;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MGB)) {
            jewelryResourceId = R.drawable.product_jewelry_name_32;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_WBCB) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WBCB_NEW)) {
            jewelryResourceId = R.drawable.product_jewelry_name_23;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC)) {
            jewelryResourceId = R.drawable.product_jewelry_name_lc;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SDP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_sdp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SDB)) {
            jewelryResourceId = R.drawable.product_jewelry_name_sdb;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_WL)) {
            jewelryResourceId = R.drawable.product_jewelry_name_wl;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_mwp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWE)) {
            jewelryResourceId = R.drawable.product_jewelry_name_mwe;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWR)) {
            jewelryResourceId = R.drawable.product_jewelry_name_mwr;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SA) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SAL) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SA2) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SA3)) {
            jewelryResourceId = R.drawable.product_jewelry_name_apple;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GFP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_gfp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GFB) || jewName.equals(BleParams.JEWELRY_BLE_NAME_GMB)) {
            jewelryResourceId = R.drawable.jewelry_icon_lmc;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GMP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_gmp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_LPP)) {
            jewelryResourceId = R.drawable.product_jewelry_name_lpp;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_LLF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_LLM)) {
            jewelryResourceId = R.drawable.jewelry_icon_ll1;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_KF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_KM)) {
            jewelryResourceId = R.drawable.jewelry_icon_ll2;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_UF)) {
            jewelryResourceId = R.drawable.product_jewelry_name_uf;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_UM)) {
            jewelryResourceId = R.drawable.product_jewelry_name_um;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_EDBF)) {
            jewelryResourceId = R.drawable.product_jewelry_name_edbf;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_EDBM)) {
            jewelryResourceId = R.drawable.product_jewelry_name_edbm;
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBM) || jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBMN)) {
            jewelryResourceId = R.drawable.product_jewelry_name_emb;
        } else if (jewName.startsWith(BleParams.JEWELRY_BLE_NAME_81)) {
            jewelryResourceId = R.drawable.jewelry_icon_81;
        } else if (jewName.startsWith(BleParams.JEWELRY_BLE_NAME_80)) {
            jewelryResourceId = R.drawable.jewelry_icon_sm2;
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_82)) {
            jewelryResourceId = R.drawable.jewelry_icon_82;
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_83)) {
            jewelryResourceId = R.drawable.jewelry_icon_83;
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_84)) {
            jewelryResourceId = R.drawable.jewelry_icon_84;
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_90)) {
            jewelryResourceId = R.drawable.jewelry_icon_90;
        } else {
            jewelryResourceId = getNfcJewelryResId(jewName);
//                if (jewelryResourceId == 0) {
//                    jewelryResourceId = R.drawable.product_jewelry_name_bmp;
//                }
        }
        return jewelryResourceId;
    }

    private static int getNfcJewelryResId(String jewName) {
        switch (jewName) {
            case JEWELRY_NFC_NAME_MEET_001:
                return R.drawable.jewelry_icon_meet_001;
            case JEWELRY_NFC_NAME_MEET_002:
                return R.drawable.jewelry_icon_meet_002;
            default:
                return 0;
        }
    }

    public static String getTranName(String jewName, Context context) {
        String tranName;
        if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_MP_NEW)) {
            tranName = context.getString(R.string.jewelry_name_wqp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MB)) {
            tranName = context.getString(R.string.jewelry_name_wqb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_DP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_DP_NEW)) {
            tranName = context.getString(R.string.jewelry_name_wbp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_DB) || jewName.equals(BleParams.JEWELRY_BLE_NAME_DB_NEW)) {
            tranName = context.getString(R.string.jewelry_name_wbb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_BP)) {
            tranName = context.getString(R.string.jewelry_name_bbp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_BB)) {
            tranName = context.getString(R.string.jewelry_name_bbb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GP)) {
            tranName = context.getString(R.string.jewelry_name_glp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GB)) {
            tranName = context.getString(R.string.jewelry_name_glb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORYP) || jewName.equals(BleParams.JEWELRY_BLE_NAME_MEMORY_NP)) {
            tranName = context.getString(R.string.jewelry_name_mmp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SL)) {
            tranName = context.getString(R.string.jewelry_name_sl);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SH)) {
            tranName = context.getString(R.string.jewelry_name_sh);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MGB)) {
            tranName = context.getString(R.string.jewelry_name_mgb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_WBCB) || jewName.equals(BleParams.JEWELRY_BLE_NAME_WBCB_NEW)) {
            tranName = context.getString(R.string.jewelry_name_wbcb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SC)) {
            tranName = context.getString(R.string.jewelry_name_sc);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SDP)) {
            tranName = context.getString(R.string.jewelry_name_sdp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SDB)) {
            tranName = context.getString(R.string.jewelry_name_sdb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_WL)) {
            tranName = context.getString(R.string.jewelry_name_wl);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWP)) {
            tranName = context.getString(R.string.jewelry_name_mwp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWE)) {
            tranName = context.getString(R.string.jewelry_name_mwe);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_MWR)) {
            tranName = context.getString(R.string.jewelry_name_mwr);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_SA) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SAL) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SA2) || jewName.equals(BleParams.JEWELRY_BLE_NAME_SA3)) {
            tranName = context.getString(R.string.jewelry_name_apple);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GFP)) {
            tranName = context.getString(R.string.jewelry_name_gfp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GFB)) {
            tranName = context.getString(R.string.jewelry_name_gfb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GMP)) {
            tranName = context.getString(R.string.jewelry_name_gmp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_GMB)) {
            tranName = context.getString(R.string.jewelry_name_gmb);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_LPP)) {
            tranName = context.getString(R.string.jewelry_name_lpp);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_LLF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_LLM)) {
            tranName = context.getString(R.string.jewelry_name_llf);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_KF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_KM)) {
            tranName = context.getString(R.string.jewelry_type_ll2);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_UF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_UM)) {
            tranName = context.getString(R.string.jewelry_name_uf);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_EDBF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_EDBM)) {
            tranName = context.getString(R.string.jewelry_name_edbf);
        } else if (jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBF) || jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBM) || jewName.equals(BleParams.JEWELRY_BLE_NAME_EMBMN)) {
            tranName = context.getString(R.string.jewelry_name_embf);
        } else if (jewName.startsWith(BleParams.JEWELRY_BLE_NAME_81)) {
            tranName = context.getString(R.string.jewelry_type_81);
        } else if (jewName.startsWith(BleParams.JEWELRY_BLE_NAME_80)) {
            tranName = context.getString(R.string.jewelry_type_sm2);
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_82)) {
            tranName = context.getString(R.string.jewelry_type_82);
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_83)) {
            tranName = context.getString(R.string.jewelry_type_83);
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_84)) {
            tranName = context.getString(R.string.jewelry_type_84);
        } else if (jewName.startsWith(JEWELRY_BLE_NAME_90)) {
            tranName = context.getString(R.string.JEWELAY_BLE_NAME_90);
        } else {
            tranName = getNfcJewelryDisplayName(context, jewName);
            if (tranName == null) {
                tranName = "";
            }
        }
        return tranName;
    }

    private static String getNfcJewelryDisplayName(Context context, String jewName) {
        switch (jewName) {
            case JEWELRY_NFC_NAME_MEET_001:
                return context.getString(R.string.jewelry_name_meet_001);
            case JEWELRY_NFC_NAME_MEET_002:
                return context.getString(R.string.jewelry_name_meet_002);
            default:
                return null;
        }
    }

    public static boolean isPendant(String name) {
        return !TextUtils.isEmpty(name);
    }

    public static boolean isSecurityJewlery() {
        return isSecurityJewlery(getConnectedJewName());
    }

    public static boolean isSecurityJewlery(String name) {
        return !TextUtils.isEmpty(name) && name.startsWith("TWO4");
    }

    //是不是贝母片首饰
    public static boolean isMWJewlery(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MWP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MWE) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_MWR);
    }

    //是不是长条首饰
    public static boolean isCtJewlery(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_90);
    }

    public static boolean isCtJewlery() {
        return TextUtils.equals(getConnectedJewName().trim(), JEWELRY_BLE_NAME_90);
    }


    public static boolean isJewlery32() {
        return TextUtils.equals(getConnectedJewName().trim(), JEWELRY_BLE_NAME_MGB);
    }

   public static boolean isJewlery23() {
        return TextUtils.equals(getConnectedJewName().trim(), JEWELRY_BLE_NAME_WBCB_NEW);
    }

    public static boolean is33Jewlery() {
        return TextUtils.equals(getConnectedJewName().trim(), JEWELRY_BLE_NAME_MWP);
    }

    public static boolean is34Jewlery() {
        return TextUtils.equals(getConnectedJewName().trim(), JEWELRY_BLE_NAME_MWE);
    }




    public static boolean isMWJewlery() {
        return isMWJewlery(getConnectedJewName());
    }

    public static boolean isLollipopJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_LPP);
    }

    public static boolean isLollipopJewelry() {
        return isLollipopJewelry(getConnectedJewName());
    }

    //智爱天使和贝母片可以喝水、备忘录、大姨妈提醒 注：这个是判断提醒的，不是判断是不是贝母片首饰的
    public static boolean isReminderJewlery() {
        return isReminderJewlery(getConnectedJewName());
    }

    public static boolean isReminderJewlery(String name) {
        return isMWJewlery(name) || TextUtils.equals(name, JEWELRY_BLE_NAME_SL) || isLollipopJewelry();
    }

    //是不是许愿首饰
    public static boolean isWishJewlery(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_SC) || TextUtils.equals(name, JEWELRY_BLE_NAME_WL);
    }

    public static boolean isWishJewlery() {
        return isWishJewlery(getConnectedJewName());
    }

    //是不是密语语首饰
    public static boolean isCodeJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_GFB) || TextUtils.equals(name, JEWELRY_BLE_NAME_GFP) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_GMB) || TextUtils.equals(name, JEWELRY_BLE_NAME_GMP);
    }

    public static boolean isCodeJewelry() {
        return isCodeJewelry(getConnectedJewName());
    }

    public static boolean isCodeBangle() {
        return isCodeBangle(getConnectedJewName());
    }

    //是不是密语语首饰
    public static boolean isCodeBangle(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_GFB) || TextUtils.equals(name, JEWELRY_BLE_NAME_GMB);
    }

    public static boolean isLoveLetter() {
        return isLoveLetter(getConnectedJewName());
    }

    /**
     * 获取当前连接的首饰的名称
     *
     * @return
     */
    public static String getConnectedJewName() {
        return PreferencesUtils.getString(ToTwooApplication.baseContext, BleParams.PAIRED_JEWELRY_NAME_TAG, "");
    }

    /**
     * 用于判定是否包含闪光功能
     *
     * @return
     */
    public static boolean isNoFlashJewelry() {
        String name = getConnectedJewName();

        return isButtonBatteryJewelry(name) || isSM2(name) || isCtJewlery();
    }


    /**
     * 新款的 TWO80, TWO81, 触摸 + 震动
     *
     * @return
     */
    public static boolean isSM2() {
        return isSM2(getConnectedJewName());
    }

    public static boolean isSM2(String name) {
        return name.startsWith(JEWELRY_BLE_NAME_80)
                || name.startsWith(JEWELRY_BLE_NAME_81)
                || name.startsWith(JEWELRY_BLE_NAME_82)
                || name.startsWith(JEWELRY_BLE_NAME_83)
                || name.startsWith(JEWELRY_BLE_NAME_84)
                || name.startsWith(JEWELRY_BLE_NAME_85)
                || name.startsWith(JEWELRY_BLE_NAME_86)
                || name.startsWith(JEWELRY_BLE_NAME_87)
                || name.startsWith(JEWELRY_BLE_NAME_88)
                || name.startsWith(JEWELRY_BLE_NAME_89);
    }


    //82系列 不支持首饰爱你
    public static boolean is82(String name) {
        return TextUtils.equals(name,JEWELRY_BLE_NAME_82);
    }

    public static boolean is82() {
        return is82(getConnectedJewName());
    }

    public static boolean is82_01() {
        return TextUtils.equals(getConnectedJewName(),JEWELRY_BLE_NAME_82_01);
    }

    public static boolean is83() {
        return TextUtils.equals(getConnectedJewName(),JEWELRY_BLE_NAME_83);
    }


    public static boolean is84() {
        return TextUtils.equals(getConnectedJewName(),JEWELRY_BLE_NAME_84);

    }




    //是不是密语语首饰
    public static boolean isLoveLetter(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_LLF) || TextUtils.equals(name, JEWELRY_BLE_NAME_LLM)
                || TextUtils.equals(name, JEWELRY_BLE_NAME_KF) || TextUtils.equals(name, JEWELRY_BLE_NAME_KM)
                || TextUtils.equals(name, JEWELRY_BLE_NAME_UF) || TextUtils.equals(name, JEWELRY_BLE_NAME_UM);
    }


    //是不是时光记忆首饰
    public static boolean isMemoryJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORYP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORY_NP);
    }

    public static boolean isMemoryJewelry() {
        return isMemoryJewelry(getConnectedJewName());
    }

    //是不是纽扣电池首饰
    public static boolean isButtonBatteryJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_EDBF) || TextUtils.equals(name, JEWELRY_BLE_NAME_EMBF) || TextUtils.equals(name, JEWELRY_BLE_NAME_EDBM)
                || TextUtils.equals(name, JEWELRY_BLE_NAME_EMBM) || TextUtils.equals(name, JEWELRY_BLE_NAME_EMBMN);
    }

    /**
     * 是否触摸首饰
     *
     * @return
     */
    public static boolean isTouchJewelry() {
        return isTouchJewelry(getConnectedJewName());
    }

    /**
     * 能自定义触摸颜色
     *
     * @return
     */
    public static boolean canChangeTouchColor() {
        return canChangeTouchColor(getConnectedJewName());
    }

    /**
     * 新款首饰(TWO82)面板颜色偏紫，所以造成颜色有偏差，需要隐藏粉黄白橙(PYWO)四种颜色，只显示红绿蓝紫青五种颜色
     * <p>
     * 注意：app内去掉粉色的色块，但是保留粉色的色值，也就是app内紫色色块保留但是该色块用粉色的色值
     *
     * @return
     */
    public static boolean needRemovePYWOColor() {
        return getConnectedJewName().startsWith(JEWELRY_BLE_NAME_82);
    }


    public static String getDefaultTotwooColor(boolean isMissYou) {
        if (needRemovePYWOColor()) {
            return isMissYou ? "RED" : "PURPLE";
        } else {
            return isMissYou ? "PINK" : "PURPLE";
        }
    }

    /**
     * 能自定义触摸颜色
     *
     * @return
     */
    public static boolean canChangeTouchColor(String name) {
        return isSM2(name) || isButtonBatteryJewelry(name);
    }


    /**
     * 是否触摸首饰
     *
     * @return
     */
    public static boolean isTouchJewelry(String name) {
        return isMWJewlery(name) || isButtonBatteryJewelry(name) || isSM2(name) || isCtJewlery();
    }


    public static boolean isButtonBatteryJewelry() {
        return isButtonBatteryJewelry(getConnectedJewName());
    }

    public static boolean isNoneMusicJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MB) || TextUtils.equals(name, JEWELRY_BLE_NAME_DP) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_DB) || TextUtils.equals(name, JEWELRY_BLE_NAME_BP) || TextUtils.equals(name, JEWELRY_BLE_NAME_BB) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_GP) || TextUtils.equals(name, JEWELRY_BLE_NAME_GB) || TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORYP) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_LLF) || TextUtils.equals(name, JEWELRY_BLE_NAME_LLM) || TextUtils.equals(name, JEWELRY_BLE_NAME_KF) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_KM) || TextUtils.equals(name, JEWELRY_BLE_NAME_UF) || TextUtils.equals(name, JEWELRY_BLE_NAME_UM) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_LL7) || TextUtils.equals(name, JEWELRY_BLE_NAME_LL8) || TextUtils.equals(name, JEWELRY_BLE_NAME_LL9) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_EDBF) || TextUtils.equals(name, JEWELRY_BLE_NAME_EDBM) || TextUtils.equals(name, JEWELRY_BLE_NAME_EMBF) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_EMBM) || TextUtils.equals(name, JEWELRY_BLE_NAME_EMBMN) || isSM2(name);
    }

    public static boolean isNoneMusicJewelry() {
        return isNoneMusicJewelry(getConnectedJewName());
    }

    public static boolean isNoneTripleClickJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MB) || TextUtils.equals(name, JEWELRY_BLE_NAME_DP) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_DB) || TextUtils.equals(name, JEWELRY_BLE_NAME_BP) || TextUtils.equals(name, JEWELRY_BLE_NAME_BB) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_GP) || TextUtils.equals(name, JEWELRY_BLE_NAME_GB) || TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORYP);
    }

    public static boolean isNoneTripleClickJewelry() {
        return isNoneTripleClickJewelry(getConnectedJewName());
    }

    public static boolean isNoOtaJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MB) || TextUtils.equals(name, JEWELRY_BLE_NAME_DP) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_DB) || TextUtils.equals(name, JEWELRY_BLE_NAME_BP) || TextUtils.equals(name, JEWELRY_BLE_NAME_BB) ||
                TextUtils.equals(name, JEWELRY_BLE_NAME_GP) || TextUtils.equals(name, JEWELRY_BLE_NAME_GB) || TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORYP);
    }

    public static boolean isNoOtaJewelry() {
        return isNoOtaJewelry(getConnectedJewName());
    }

    public static boolean isNoLoveJewelry(String name) {
        return TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORYP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MEMORY_NP) || TextUtils.equals(name, JEWELRY_BLE_NAME_MWP)
                || TextUtils.equals(name, JEWELRY_BLE_NAME_MWE) || TextUtils.equals(name, JEWELRY_BLE_NAME_MWR);
    }

    public static boolean isNoLoveJewelry() {
        return isNoLoveJewelry(getConnectedJewName());
    }

    /**
     * 是否蓝牙首饰, 基于目前项目状态, 非 NFC 首饰即为蓝牙首饰; 后续如果追加更多类型, 再修改此方法即可
     *
     * @param name
     * @return
     */
    public static boolean isBluetoothJewelry(@Nullable String name) {
        if (name == null) {
            name = getConnectedJewName();
        }
        return !TextUtils.isEmpty(name) && !isNfcJewelry(name);
    }

    public static boolean isNfcJewelry(@Nullable String name) {
        if (name == null) {
            name = getConnectedJewName();
        }
        return name.startsWith(JEWELRY_NFC_NAME_PREF);
    }

    public static boolean isOtaNameMatch(String otaName) {
        String name = getConnectedJewName();
        switch (name) {
            case JEWELRY_BLE_NAME_MP:
            case JEWELRY_BLE_NAME_MB:
            case JEWELRY_BLE_NAME_MEMORYP:
                return TextUtils.equals(otaName, OTA_DEVICES_NAME);
            case JEWELRY_BLE_NAME_DP:
            case JEWELRY_BLE_NAME_DB:
            case JEWELRY_BLE_NAME_BP:
            case JEWELRY_BLE_NAME_BB:
            case JEWELRY_BLE_NAME_GP:
            case JEWELRY_BLE_NAME_GB:
                return TextUtils.equals(otaName, OTA_DEVICES_NAME_NEW);
            case JEWELRY_BLE_NAME_SL:
            case JEWELRY_BLE_NAME_SH:
            case JEWELRY_BLE_NAME_MP_NEW:
            case JEWELRY_BLE_NAME_DP_NEW:
            case JEWELRY_BLE_NAME_DB_NEW:
            case JEWELRY_BLE_NAME_MEMORY_NP:
            case JEWELRY_BLE_NAME_MGB:
            case JEWELRY_BLE_NAME_WBCB:
            case JEWELRY_BLE_NAME_WBCB_NEW:
            case JEWELRY_BLE_NAME_SC:
            case JEWELRY_BLE_NAME_SDP:
            case JEWELRY_BLE_NAME_SDB:
            case JEWELRY_BLE_NAME_WL:
            case JEWELRY_BLE_NAME_MWP:
            case JEWELRY_BLE_NAME_MWE:
            case JEWELRY_BLE_NAME_MWR:
                return TextUtils.equals(otaName, OTA_DEVICES_NAME_BANK);
            case JEWELRY_BLE_NAME_SA:
            case JEWELRY_BLE_NAME_SAL:
            case JEWELRY_BLE_NAME_SA2:
            case JEWELRY_BLE_NAME_SA3:
            case JEWELRY_BLE_NAME_GFP:
            case JEWELRY_BLE_NAME_GFB:
            case JEWELRY_BLE_NAME_GMP:
            case JEWELRY_BLE_NAME_GMB:
            case JEWELRY_BLE_NAME_LLF:
            case JEWELRY_BLE_NAME_LLM:
            case JEWELRY_BLE_NAME_KF:
            case JEWELRY_BLE_NAME_KM:
            case JEWELRY_BLE_NAME_UF:
            case JEWELRY_BLE_NAME_UM:
            case JEWELRY_BLE_NAME_LL7:
            case JEWELRY_BLE_NAME_LL8:
            case JEWELRY_BLE_NAME_LL9:
            case JEWELRY_BLE_NAME_EDBF:
            case JEWELRY_BLE_NAME_EMBF:
            case JEWELRY_BLE_NAME_EDBM:
            case JEWELRY_BLE_NAME_EMBM:
            case JEWELRY_BLE_NAME_EMBMN:
                return TextUtils.equals(otaName, OTA_DEVICES_NAME_SAFE);
        }
        return false;
    }

    public void test() {
        try {
            ArrayList<CustomOrderBean> data = (ArrayList<CustomOrderBean>) CustomOrderDbHelper.getInstance().getSelect(1);
        } catch (DbException e) {
            e.printStackTrace();
        }
    }


    public static final String PREF_RECHECK = "reCheckInterval";


    public static final int DEFAULT_RETRY_INTERVAL = 1000 * 2;
    public static final int MAXIMUM_RETRY_INTERVAL = 1000 * 20;
    public static final int NOTIFY_INTERVAL = 1000 * 60 * 3;

    /**
     * 扫描时间
     */
    public final static long SCAN_DURATION = 20000;

    public final static String UUID_CHAR_CONTROL = "fd0a";  //写ota命令通道
    public final static String UUID_CHAR_CONTROL_NOTIFY = "fd09";   //"写ota命令"的返回通道
    public static final String UUID_CHAR_BATTERY_LEVEL = "2a19";    //读取电量百分比
    public static final String UUID_CHAR_SYSTEM_ID = "2a23";    //读取硬件的唯一识别标示通道
    public static final String UUID_CHAR_FIRMWARE_REVISION = "2a28";    //读取固件版本号
    public static final String UUID_CHARGE_STATE_CHAR = "2af0"; //读取首饰充电状态，0为充电，1在充电
    public static final String UUID_CHAR_WRITE = "fd1a";//写4X命令,除了升级模式外所有写命令都走这里
    public static final String UUID_CHAR_NOTIFY = "fd19";    //读取fd1a命令的返回


    public static final String UUID_CHAR_MODEL_NUMBER = "2a24";    // NB16

    public static final String UUID_CHAR_HARDWARE_REVISION = "2a27";

    public static final String UUID_CHAR_PSN = "2a25";//psn


}
