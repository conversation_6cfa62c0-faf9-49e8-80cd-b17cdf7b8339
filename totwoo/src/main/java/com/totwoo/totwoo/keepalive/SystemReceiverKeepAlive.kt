package com.totwoo.totwoo.keepalive

import android.bluetooth.BluetoothAdapter
import android.content.BroadcastReceiver
import android.content.Context
import android.content.Intent
import android.content.IntentFilter
import android.net.ConnectivityManager
import android.os.Build
import android.os.Handler
import android.os.Looper
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.service.KeepAliveService

/**
 * 保活系统事件广播接收器 - 适配Android高版本限制
 *
 * 功能：
 * 1. 静态注册：开机启动、应用更新、电源状态、蓝牙状态、网络状态
 * 2. 动态注册：屏幕开关事件（Android 8.0+无法静态注册）
 * 3. 触发保活机制恢复
 *
 * 注意：Android 8.0+ 对隐式广播有严格限制，部分事件需要动态注册
 */
class SystemReceiverKeepAlive : BroadcastReceiver() {

    companion object {
        private const val TAG = "SystemReceiverKeepAlive"

        // 防抖机制 - 避免频繁触发
        private var lastScreenEventTime = 0L
        private var lastBluetoothEventTime = 0L
        private var lastNetworkEventTime = 0L
        private const val EVENT_DEBOUNCE_INTERVAL = 3000L // 3秒防抖

        // 动态注册的接收器实例（用于屏幕开关事件）
        private var dynamicReceiver: SystemReceiverKeepAlive? = null

        /**
         * 注册动态广播接收器（用于屏幕开关事件）
         * Android 8.0+ 屏幕开关事件无法静态注册，需要动态注册
         */
        fun registerDynamicReceiver(context: Context) {
            if (dynamicReceiver != null) {
                LogUtils.d(TAG, "动态广播接收器已注册，跳过")
                return
            }

            try {
                dynamicReceiver = SystemReceiverKeepAlive()
                val filter = IntentFilter().apply {
                    // 屏幕开关事件（Android 8.0+无法静态注册）
                    addAction(Intent.ACTION_SCREEN_ON)
                    addAction(Intent.ACTION_SCREEN_OFF)
                    addAction(Intent.ACTION_USER_PRESENT) // 用户解锁
                    priority = 1000
                }

                context.registerReceiver(dynamicReceiver, filter)
                LogUtils.d(TAG, "动态广播接收器注册成功")
            } catch (e: Exception) {
                LogUtils.e(TAG, "动态广播接收器注册失败: ${e.message}")
                dynamicReceiver = null
            }
        }

        /**
         * 注销动态广播接收器
         */
        fun unregisterDynamicReceiver(context: Context) {
            dynamicReceiver?.let { receiver ->
                try {
                    context.unregisterReceiver(receiver)
                    LogUtils.d(TAG, "动态广播接收器注销成功")
                } catch (e: Exception) {
                    LogUtils.e(TAG, "动态广播接收器注销失败: ${e.message}")
                } finally {
                    dynamicReceiver = null
                }
            }
        }
    }

    override fun onReceive(context: Context?, intent: Intent?) {
        if (context == null || intent?.action == null) return

        val action = intent.action!!
        val currentTime = System.currentTimeMillis()
        LogUtils.d(TAG, "📡 收到系统广播: $action")

        // 防抖检查
        if (isEventDebounced(action, currentTime)) {
            LogUtils.d(TAG, "事件防抖，跳过处理: $action")
            return
        }

        when (action) {
            Intent.ACTION_SCREEN_ON -> {
                LogUtils.d(TAG, "屏幕点亮")
                checkAndRestartServices(context, "SCREEN_ON")
            }

            Intent.ACTION_SCREEN_OFF -> {
                LogUtils.d(TAG, "屏幕关闭")
                checkAndRestartServices(context, "SCREEN_OFF")
            }

            Intent.ACTION_USER_PRESENT -> {
                LogUtils.d(TAG, "用户解锁")
                checkAndRestartServices(context, "USER_PRESENT")
            }

            ConnectivityManager.CONNECTIVITY_ACTION -> {
                LogUtils.d(TAG, "网络状态变化")
                checkAndRestartServices(context, "NETWORK_CHANGED")
            }

            Intent.ACTION_BOOT_COMPLETED -> {
                LogUtils.d(TAG, "设备开机完成")
                handleBootCompleted(context)
            }

            Intent.ACTION_MY_PACKAGE_REPLACED,
            Intent.ACTION_PACKAGE_REPLACED -> {
                LogUtils.d(TAG, "应用更新完成")
                handleBootCompleted(context)
            }

            Intent.ACTION_POWER_CONNECTED -> {
                LogUtils.d(TAG, "电源连接")
                checkAndRestartServices(context, "POWER_CONNECTED")
            }

            Intent.ACTION_POWER_DISCONNECTED -> {
                LogUtils.d(TAG, "电源断开")
                checkAndRestartServices(context, "POWER_DISCONNECTED")
            }

            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                val state = intent.getIntExtra(BluetoothAdapter.EXTRA_STATE, BluetoothAdapter.ERROR)
                LogUtils.d(TAG, "蓝牙状态变化: $state")
                checkAndRestartServices(context, "BLUETOOTH_STATE_CHANGED")
            }

            else -> {
                LogUtils.d(TAG, "未处理的广播事件: $action")
            }
        }
    }

    /**
     * 处理设备重启完成或应用更新
     */
    private fun handleBootCompleted(context: Context) {
        LogUtils.d(TAG, "处理开机启动或应用更新")

        // 延迟启动，等待系统完全启动
        Handler(Looper.getMainLooper()).postDelayed({
            try {
                // 注册动态广播接收器
                registerDynamicReceiver(context)

                // 启动保活服务
                checkAndStartServices(context, "BOOT_COMPLETED")
            } catch (e: Exception) {
                LogUtils.e(TAG, "开机启动处理失败: ${e.message}")
            }
        }, 10000) // 延迟10秒
    }

    /**
     * 检查并重启服务 - 直接启动
     */
    private fun checkAndRestartServices(context: Context, trigger: String) {
        try {
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 检查并启动服务 - 用于开机启动
     */
    private fun checkAndStartServices(context: Context, trigger: String) {
        try {
            val serviceIntent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_START
                putExtra("trigger", trigger)
            }

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
                context.startForegroundService(serviceIntent)
            } else {
                context.startService(serviceIntent)
            }

            LogUtils.d(TAG, "服务启动成功: $trigger")
        } catch (e: Exception) {
            LogUtils.e(TAG, "服务启动失败: ${e.message}")
        }
    }

    /**
     * 检查事件是否需要防抖
     */
    private fun isEventDebounced(action: String, currentTime: Long): Boolean {
        return when (action) {
            Intent.ACTION_SCREEN_ON,
            Intent.ACTION_SCREEN_OFF,
            Intent.ACTION_USER_PRESENT -> {
                if (currentTime - lastScreenEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    true
                } else {
                    lastScreenEventTime = currentTime
                    false
                }
            }

            BluetoothAdapter.ACTION_STATE_CHANGED -> {
                if (currentTime - lastBluetoothEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    true
                } else {
                    lastBluetoothEventTime = currentTime
                    false
                }
            }

            ConnectivityManager.CONNECTIVITY_ACTION -> {
                if (currentTime - lastNetworkEventTime < EVENT_DEBOUNCE_INTERVAL) {
                    true
                } else {
                    lastNetworkEventTime = currentTime
                    false
                }
            }

            else -> false
        }
    }
}
