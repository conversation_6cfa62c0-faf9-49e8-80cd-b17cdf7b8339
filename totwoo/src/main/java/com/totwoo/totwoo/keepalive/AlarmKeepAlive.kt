package com.totwoo.totwoo.keepalive

import android.app.AlarmManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import com.tencent.mars.xlog.Log
import com.totwoo.library.util.Apputils
import com.totwoo.library.util.LogUtils
import com.totwoo.totwoo.ble.BleParams
import com.totwoo.totwoo.service.KeepAliveService

/**
 * 闹钟保活组件 - Kotlin简化版
 * 
 * 职责：
 * 1. 提供基本的心跳检查（作为CoroutineWorker的补充）
 * 2. 在现代Android中作用有限，主要用于兜底
 * 3. 5分钟间隔，降低频率减少电池消耗
 */
class AlarmKeepAlive(private val context: Context) {

    companion object {
        private const val TAG = "AlarmKeepAlive"
        private const val KEEP_ALIVE_INTERVAL = 5 * 60 * 1000L // 5分钟
    }

    private var isActive = false

    /**
     * 启动闹钟保活
     */
    fun start() {
        if (isActive) {
            LogUtils.d(TAG, "闹钟保活已启动")
            return
        }

        try {
            scheduleKeepAlive()
            isActive = true
            Log.i(TAG, "⏰ 闹钟保活启动成功 (间隔: 5分钟, 仅Android 8-11)")
        } catch (e: Exception) {
            Log.e(TAG, "启动闹钟保活失败: ${e.message}")
        }
    }

    /**
     * 停止闹钟保活
     */
    fun stop() {
        if (!isActive) {
            return
        }

        try {
            cancelKeepAlive()
            isActive = false
            Log.i(TAG, "闹钟保活已停止")
        } catch (e: Exception) {
            Log.e(TAG, "停止闹钟保活失败: ${e.message}")
        }
    }

    /**
     * 检查是否活跃
     */
    fun isActive(): Boolean = isActive

    // ==================== 私有方法 ====================

    /**
     * 调度保活闹钟
     */
    private fun scheduleKeepAlive() {
        try {
            // 直接设置闹钟触发KeepAliveService
            val intent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
                putExtra("trigger", TAG)
            }

            val pendingIntent = PendingIntent.getService(
                context, 0, intent,
                Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT)
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
                ?: throw IllegalStateException("AlarmManager不可用")

            val triggerTime = System.currentTimeMillis() + KEEP_ALIVE_INTERVAL

            when {
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.S && alarmManager.canScheduleExactAlarms() -> {
                    alarmManager.setExactAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
                Build.VERSION.SDK_INT >= Build.VERSION_CODES.M -> {
                    alarmManager.setAndAllowWhileIdle(AlarmManager.RTC_WAKEUP, triggerTime, pendingIntent)
                }
                else -> {
                    alarmManager.setRepeating(AlarmManager.RTC_WAKEUP, triggerTime, KEEP_ALIVE_INTERVAL, pendingIntent)
                }
            }
        } catch (e: IllegalStateException) {
            Log.e(TAG, "调度重连闹钟失败: ${e.message}")
        }
    }

    /**
     * 取消保活闹钟
     */
    private fun cancelKeepAlive() {
        try {
            val intent = Intent(context, KeepAliveService::class.java).apply {
                action = BleParams.ACTION_KEEP_ALIVE
            }

            val pendingIntent = PendingIntent.getService(
                context, 0, intent,
                Apputils.wrapMutablePendingFlag(PendingIntent.FLAG_UPDATE_CURRENT)
            )

            val alarmManager = context.getSystemService(Context.ALARM_SERVICE) as? AlarmManager
            alarmManager?.cancel(pendingIntent)
        } catch (e: Exception) {
            LogUtils.e(TAG, "取消重连闹钟失败: ${e.message}")
        }
    }
}
