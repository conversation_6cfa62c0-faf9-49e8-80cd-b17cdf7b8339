package com.totwoo.totwoo.push;

import android.content.Context;
import android.util.Log;

import com.xiaomi.mipush.sdk.ErrorCode;
import com.xiaomi.mipush.sdk.MiPushClient;
import com.xiaomi.mipush.sdk.MiPushCommandMessage;
import com.xiaomi.mipush.sdk.MiPushMessage;
import com.xiaomi.mipush.sdk.PushMessageReceiver;

import java.util.List;

import cn.jiguang.plugins.push.JPushMiInterface;

/**
 * 小米推送消息接收器
 * 
 * 处理小米推送的各种消息和事件
 * 主要用于小米设备的推送服务
 */
public class MiPushReceiver extends PushMessageReceiver {
    private static final String TAG = "MiPushReceiver";

    @Override
    public void onReceivePassThroughMessage(Context context, MiPushMessage message) {
        Log.d(TAG, "收到小米透传消息: " + message.getContent());
        
        try {
            // 将小米推送消息转发给极光推送处理
            JPushMiInterface.onReceivePassThroughMessage(context, message);
        } catch (Exception e) {
            Log.e(TAG, "处理小米透传消息失败: " + e.getMessage());
        }
    }

    @Override
    public void onNotificationMessageClicked(Context context, MiPushMessage message) {
        Log.d(TAG, "小米推送通知被点击: " + message.getContent());
        
        try {
            // 将小米推送通知点击事件转发给极光推送处理
            JPushMiInterface.onNotificationMessageClicked(context, message);
        } catch (Exception e) {
            Log.e(TAG, "处理小米推送通知点击失败: " + e.getMessage());
        }
    }

    @Override
    public void onNotificationMessageArrived(Context context, MiPushMessage message) {
        Log.d(TAG, "收到小米推送通知: " + message.getContent());
        
        try {
            // 将小米推送通知到达事件转发给极光推送处理
            JPushMiInterface.onNotificationMessageArrived(context, message);
        } catch (Exception e) {
            Log.e(TAG, "处理小米推送通知到达失败: " + e.getMessage());
        }
    }

    @Override
    public void onCommandResult(Context context, MiPushCommandMessage message) {
        Log.d(TAG, "小米推送命令结果: " + message.getCommand());
        
        try {
            // 将小米推送命令结果转发给极光推送处理
            JPushMiInterface.onCommandResult(context, message);
        } catch (Exception e) {
            Log.e(TAG, "处理小米推送命令结果失败: " + e.getMessage());
        }
    }

    @Override
    public void onReceiveRegisterResult(Context context, MiPushCommandMessage message) {
        Log.d(TAG, "小米推送注册结果: " + message.getResultCode());
        
        try {
            if (message.getResultCode() == ErrorCode.SUCCESS) {
                String regId = message.getCommandArguments().get(0);
                Log.d(TAG, "小米推送注册成功，RegId: " + regId);
                
                // 将注册结果转发给极光推送处理
                JPushMiInterface.onReceiveRegisterResult(context, message);
            } else {
                Log.e(TAG, "小米推送注册失败: " + message.getReason());
            }
        } catch (Exception e) {
            Log.e(TAG, "处理小米推送注册结果失败: " + e.getMessage());
        }
    }
}
