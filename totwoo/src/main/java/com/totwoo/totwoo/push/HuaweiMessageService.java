package com.totwoo.totwoo.push;

import android.util.Log;

import com.huawei.hms.push.HmsMessageService;
import com.huawei.hms.push.RemoteMessage;

import cn.jiguang.plugins.push.JPushHuaweiInterface;

/**
 * 华为推送消息服务
 * 
 * 处理华为 HMS Push 推送消息
 * 主要用于华为设备的推送服务
 */
public class HuaweiMessageService extends HmsMessageService {
    private static final String TAG = "HuaweiMessageService";

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        
        Log.d(TAG, "收到华为推送消息: " + remoteMessage.getFrom());
        
        try {
            // 将华为推送消息转发给极光推送处理
            JPushHuaweiInterface.onMessageReceived(this, remoteMessage);
        } catch (Exception e) {
            Log.e(TAG, "处理华为推送消息失败: " + e.getMessage());
        }
    }

    @Override
    public void onNewToken(String token) {
        super.onNewToken(token);
        
        Log.d(TAG, "华为推送 Token 更新: " + token);
        
        try {
            // 将新的华为推送 Token 上报给极光推送
            JPushHuaweiInterface.onNewToken(this, token);
        } catch (Exception e) {
            Log.e(TAG, "上报华为推送 Token失败: " + e.getMessage());
        }
    }

    @Override
    public void onTokenError(Exception e) {
        super.onTokenError(e);
        Log.e(TAG, "华为推送 Token 错误: " + e.getMessage());
    }

    @Override
    public void onMessageSent(String msgId) {
        super.onMessageSent(msgId);
        Log.d(TAG, "华为推送消息发送成功: " + msgId);
    }

    @Override
    public void onSendError(String msgId, Exception exception) {
        super.onSendError(msgId, exception);
        Log.e(TAG, "华为推送消息发送失败: " + msgId + ", 错误: " + exception.getMessage());
    }
}
