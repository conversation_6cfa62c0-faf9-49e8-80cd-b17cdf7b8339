package com.totwoo.totwoo.push;

import android.util.Log;

import com.google.firebase.messaging.FirebaseMessagingService;
import com.google.firebase.messaging.RemoteMessage;

import cn.jiguang.plugins.push.JPushFCMInterface;

/**
 * FCM 推送消息服务
 * 
 * 处理 Firebase Cloud Messaging 推送消息
 * 主要用于海外用户的推送服务
 */
public class FCMMessageService extends FirebaseMessagingService {
    private static final String TAG = "FCMMessageService";

    @Override
    public void onMessageReceived(RemoteMessage remoteMessage) {
        super.onMessageReceived(remoteMessage);
        
        Log.d(TAG, "收到FCM推送消息: " + remoteMessage.getFrom());
        
        try {
            // 将FCM消息转发给极光推送处理
            JPushFCMInterface.onMessageReceived(this, remoteMessage);
        } catch (Exception e) {
            Log.e(TAG, "处理FCM消息失败: " + e.getMessage());
        }
    }

    @Override
    public void onNewToken(String token) {
        super.onNewToken(token);
        
        Log.d(TAG, "FCM Token 更新: " + token);
        
        try {
            // 将新的FCM Token 上报给极光推送
            JPushFCMInterface.onNewToken(this, token);
        } catch (Exception e) {
            Log.e(TAG, "上报FCM Token失败: " + e.getMessage());
        }
    }

    @Override
    public void onDeletedMessages() {
        super.onDeletedMessages();
        Log.d(TAG, "FCM消息被删除");
    }
}
