package com.totwoo.totwoo.push;

import android.content.Context;
import android.util.Log;


/**
 * 极光推送厂商通道配置
 * 
 * 支持的厂商通道：
 * 1. FCM (Firebase Cloud Messaging) - 海外推送
 * 2. 小米推送 - 小米设备
 * 3. 华为推送 - 华为设备
 */
public class VendorPushConfig {
    private static final String TAG = "VendorPushConfig";
    
    // 小米推送配置 - 从tim模块复用配置
    private static final String MI_APP_ID = "2882303761517409136";
    private static final String MI_APP_KEY = "5331740911136";
    
    /**
     * 初始化所有厂商通道
     * @param context Application Context
     */
    public static void initVendorPush(Context context) {
        Log.d(TAG, "开始初始化厂商推送通道");
        
        // 1. 初始化FCM通道
        initFCM(context);
        
        // 2. 初始化小米通道
        initMiPush(context);
        
        // 3. 初始化华为通道
        initHuaweiPush(context);
        
        Log.d(TAG, "厂商推送通道初始化完成");
    }
    
    /**
     * 初始化FCM通道
     */
    private static void initFCM(Context context) {
        try {
            // FCM通道初始化 - 依赖google-services.json配置
            JPushFCMInterface.init(context);
            Log.d(TAG, "FCM通道初始化成功");
        } catch (Exception e) {
            Log.e(TAG, "FCM通道初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 初始化小米推送通道
     */
    private static void initMiPush(Context context) {
        try {
            // 检查是否为小米设备
            if (isXiaomiDevice()) {
                // 小米通道初始化 - 需要配置AppID和AppKey
                JPushMiInterface.init(context, MI_APP_ID, MI_APP_KEY);
                Log.d(TAG, "小米推送通道初始化成功");
            } else {
                Log.d(TAG, "非小米设备，跳过小米推送通道初始化");
            }
        } catch (Exception e) {
            Log.e(TAG, "小米推送通道初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 初始化华为推送通道
     */
    private static void initHuaweiPush(Context context) {
        try {
            // 检查是否为华为设备
            if (isHuaweiDevice()) {
                // 华为通道初始化 - 依赖agconnect-services.json配置
                JPushHuaweiInterface.init(context);
                Log.d(TAG, "华为推送通道初始化成功");
            } else {
                Log.d(TAG, "非华为设备，跳过华为推送通道初始化");
            }
        } catch (Exception e) {
            Log.e(TAG, "华为推送通道初始化失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否为小米设备
     */
    private static boolean isXiaomiDevice() {
        String manufacturer = android.os.Build.MANUFACTURER;
        return "Xiaomi".equalsIgnoreCase(manufacturer) || 
               "Redmi".equalsIgnoreCase(manufacturer) ||
               "POCO".equalsIgnoreCase(manufacturer);
    }
    
    /**
     * 检查是否为华为设备
     */
    private static boolean isHuaweiDevice() {
        String manufacturer = android.os.Build.MANUFACTURER;
        return "HUAWEI".equalsIgnoreCase(manufacturer) || 
               "HONOR".equalsIgnoreCase(manufacturer);
    }
    
    /**
     * 获取当前设备支持的推送通道信息
     */
    public static String getSupportedChannels() {
        StringBuilder channels = new StringBuilder();
        channels.append("支持的推送通道: ");
        
        if (isXiaomiDevice()) {
            channels.append("小米推送 ");
        }
        
        if (isHuaweiDevice()) {
            channels.append("华为推送 ");
        }
        
        channels.append("FCM ");
        channels.append("极光推送");
        
        return channels.toString();
    }
}
