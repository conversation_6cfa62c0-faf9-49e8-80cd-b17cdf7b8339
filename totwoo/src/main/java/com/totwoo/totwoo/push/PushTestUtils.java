package com.totwoo.totwoo.push;

import android.content.Context;
import android.util.Log;

import cn.jpush.android.api.JPushInterface;

/**
 * 推送测试工具类
 * 
 * 用于测试各厂商推送通道的工作状态
 */
public class PushTestUtils {
    private static final String TAG = "PushTestUtils";
    
    /**
     * 获取推送状态信息
     */
    public static String getPushStatusInfo(Context context) {
        StringBuilder info = new StringBuilder();
        
        try {
            // 1. 极光推送基础信息
            info.append("=== 极光推送状态 ===\n");
            info.append("注册状态: ").append(JPushInterface.isPushStopped(context) ? "已停止" : "正常").append("\n");
            info.append("Registration ID: ").append(JPushInterface.getRegistrationID(context)).append("\n");
            
            // 2. 设备信息
            info.append("\n=== 设备信息 ===\n");
            info.append("设备厂商: ").append(android.os.Build.MANUFACTURER).append("\n");
            info.append("设备型号: ").append(android.os.Build.MODEL).append("\n");
            info.append("Android版本: ").append(android.os.Build.VERSION.RELEASE).append("\n");
            
            // 3. 厂商通道支持情况
            info.append("\n=== 厂商通道支持 ===\n");
            info.append(VendorPushConfig.getSupportedChannels()).append("\n");
            
            // 4. 厂商通道状态检查
            info.append("\n=== 厂商通道状态 ===\n");
            
            if (isXiaomiDevice()) {
                info.append("小米推送: 已集成\n");
            }
            
            if (isHuaweiDevice()) {
                info.append("华为推送: 已集成\n");
            }
            
            info.append("FCM推送: 已集成\n");
            
        } catch (Exception e) {
            Log.e(TAG, "获取推送状态信息失败: " + e.getMessage());
            info.append("获取推送状态信息失败: ").append(e.getMessage());
        }
        
        return info.toString();
    }
    
    /**
     * 测试推送功能
     */
    public static void testPushFunction(Context context) {
        Log.d(TAG, "开始测试推送功能");
        
        try {
            // 1. 检查极光推送状态
            if (JPushInterface.isPushStopped(context)) {
                Log.w(TAG, "极光推送已停止，尝试恢复");
                JPushInterface.resumePush(context);
            }
            
            // 2. 获取Registration ID
            String regId = JPushInterface.getRegistrationID(context);
            Log.d(TAG, "当前Registration ID: " + regId);
            
            // 3. 设置标签和别名（测试用）
            java.util.Set<String> tags = new java.util.HashSet<>();
            tags.add("test");
            tags.add("debug");
            
            JPushInterface.setAliasAndTags(context, "test_user", tags, new cn.jpush.android.api.TagAliasCallback() {
                @Override
                public void gotResult(int code, String alias, java.util.Set<String> tags) {
                    Log.d(TAG, "设置标签和别名结果: code=" + code + ", alias=" + alias);
                }
            });
            
        } catch (Exception e) {
            Log.e(TAG, "测试推送功能失败: " + e.getMessage());
        }
    }
    
    /**
     * 检查是否为小米设备
     */
    private static boolean isXiaomiDevice() {
        String manufacturer = android.os.Build.MANUFACTURER;
        return "Xiaomi".equalsIgnoreCase(manufacturer) || 
               "Redmi".equalsIgnoreCase(manufacturer) ||
               "POCO".equalsIgnoreCase(manufacturer);
    }
    
    /**
     * 检查是否为华为设备
     */
    private static boolean isHuaweiDevice() {
        String manufacturer = android.os.Build.MANUFACTURER;
        return "HUAWEI".equalsIgnoreCase(manufacturer) || 
               "HONOR".equalsIgnoreCase(manufacturer);
    }
    
    /**
     * 获取推送通道优先级说明
     */
    public static String getPushChannelPriority() {
        StringBuilder priority = new StringBuilder();
        priority.append("=== 推送通道优先级 ===\n");
        priority.append("1. 厂商通道（华为/小米等）- 系统级推送，到达率最高\n");
        priority.append("2. FCM通道 - 海外推送，需要Google服务\n");
        priority.append("3. 极光推送 - 基础推送通道，兜底方案\n");
        priority.append("\n推送策略：\n");
        priority.append("- 优先使用设备对应的厂商通道\n");
        priority.append("- 厂商通道不可用时使用FCM\n");
        priority.append("- 最后使用极光推送作为兜底\n");
        
        return priority.toString();
    }
}
