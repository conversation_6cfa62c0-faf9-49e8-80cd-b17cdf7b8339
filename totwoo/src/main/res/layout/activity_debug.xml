<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/layer_bg_white"
    android:orientation="vertical"
    android:paddingHorizontal="15dp">

    <include layout="@layout/totwoo_topbar_layout"
        android:layout_width="match_parent"
        android:layout_height="@dimen/activity_actionbar_height"/>


    <Button
        android:id="@+id/btnShare"
        android:layout_width="120dp"
        android:layout_height="40dp"
        android:text="分享"/>

    <!-- CompanionDeviceHelper 调试区域 -->
    <TextView
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:text="CompanionDevice 调试"
        android:textSize="16sp"
        android:textStyle="bold"
        android:textColor="@color/black"/>

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="1"
        android:layout_marginTop="10dp">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical">

            <Button
                android:id="@+id/btnAutoAssociate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="1. 自动关联已配对设备"/>

            <Button
                android:id="@+id/btnRequestNotificationAccess"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="2. 申请后台运行权限"/>

            <Button
                android:id="@+id/btnStartObserving"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="3. 开始设备存在监听"/>

            <Button
                android:id="@+id/btnStopObserving"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="4. 停止设备监听"/>

            <Button
                android:id="@+id/btnDisassociate"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="5. 解除设备关联"/>

            <Button
                android:id="@+id/btnCheckStatus"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="检查关联状态"/>

            <Button
                android:id="@+id/btnTestPush"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="8dp"
                android:text="测试推送通道"/>

            <TextView
                android:id="@+id/tvDebugLog"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:padding="12dp"
                android:text="调试日志将显示在这里..."
                android:textSize="12sp"
                android:fontFamily="monospace"
                android:scrollbars="vertical"/>

        </LinearLayout>
    </ScrollView>

</LinearLayout>