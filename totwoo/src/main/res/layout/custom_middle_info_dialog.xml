<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="280dp"
    android:layout_height="wrap_content"
    android:orientation="vertical">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <ImageView
            android:id="@+id/custom_middle_cancel_pair_iv"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginBottom="@dimen/common_padding_parent"
            android:visibility="gone" />

        <LinearLayout
            android:layout_width="300dp"
            android:layout_height="wrap_content"
            android:background="@drawable/common_middle_dialog_bg"
            android:orientation="vertical"
            android:paddingBottom="32dp">

            <View
                android:layout_width="match_parent"
                android:layout_height="8dp" />

            <ImageView
                android:id="@+id/custom_middle_icon_iv"
                android:layout_width="68dp"
                android:layout_height="68dp"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                android:visibility="gone" />

            <TextView
                android:id="@+id/custom_middle_title_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="14dp"
                android:gravity="center_horizontal"
                android:textColor="@color/text_color_black_important"
                android:textSize="18sp"
                android:visibility="gone" />

            <LinearLayout
                android:id="@+id/custom_middle_info_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/common_padding_parent"
                android:layout_marginTop="9dp"
                android:layout_marginEnd="@dimen/common_padding_parent"
                android:orientation="vertical"
                android:visibility="gone">

                <TextView
                    android:id="@+id/custom_middle_info_tv1"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:lineSpacingMultiplier="1.5"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/custom_middle_info_tv2"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:lineSpacingMultiplier="1.5"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="14sp"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/custom_middle_info_tv3"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="7dp"
                    android:lineSpacingMultiplier="1.5"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="14sp"
                    android:visibility="gone" />

            </LinearLayout>

            <FrameLayout
                android:id="@+id/custom_middle_empty_fl"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginStart="@dimen/common_padding_parent"
                android:layout_marginTop="@dimen/common_padding_parent"
                android:layout_marginEnd="@dimen/common_padding_parent"
                android:visibility="gone">

            </FrameLayout>

            <androidx.constraintlayout.widget.ConstraintLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="31dp"
                android:orientation="horizontal">

                <TextView
                    android:id="@+id/custom_middle_deny_tv"
                    android:layout_width="120dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/setting_button_deny_bg"
                    android:gravity="center"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:singleLine="true"
                    android:textColor="@color/text_color_black_important"
                    android:visibility="gone"
                    app:layout_constraintEnd_toStartOf="@id/custom_middle_confirm_tv"
                    app:layout_constraintStart_toStartOf="parent"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

                <TextView
                    android:id="@+id/custom_middle_confirm_tv"
                    android:layout_width="120dp"
                    android:layout_height="36dp"
                    android:layout_gravity="center_horizontal"
                    android:background="@drawable/setting_button_sure_bg"
                    android:gravity="center"
                    android:paddingLeft="18dp"
                    android:paddingRight="18dp"
                    android:singleLine="true"
                    android:textColor="@color/white"
                    android:visibility="gone"
                    app:layout_constraintEnd_toEndOf="parent"
                    app:layout_constraintStart_toEndOf="@id/custom_middle_deny_tv"
                    app:layout_constraintTop_toTopOf="parent"
                    tools:visibility="visible" />

            </androidx.constraintlayout.widget.ConstraintLayout>

            <LinearLayout
                android:id="@+id/custom_add_ll"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <CheckBox
                    android:id="@+id/custom_add_cb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="true"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/custom_middle_info_add"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:textColor="@color/text_color_black_nomal"
                    android:visibility="gone" />

            </LinearLayout>

            <FrameLayout
                android:id="@+id/custom_add_terms_ll"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:layout_marginTop="20dp"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:paddingRight="20dp">

                <CheckBox
                    android:id="@+id/custom_add_terms_cb"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:checked="false"
                    android:visibility="gone" />

                <TextView
                    android:id="@+id/consent_privacy_description"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/terms_agree"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="12sp" />

            </FrameLayout>

            <LinearLayout
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="bottom"
                android:layout_marginLeft="20dp"
                android:orientation="horizontal"
                android:visibility="gone">

                <TextView
                    android:id="@+id/custom_add_terms_1"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/terms_policy"
                    android:textColor="@color/color_main"
                    android:textSize="12sp" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:paddingHorizontal="4dp"
                    android:text="@string/terms_and"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/custom_add_terms_private"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:singleLine="true"
                    android:text="@string/terms_private"
                    android:textColor="@color/color_main"
                    android:textSize="12sp" />
            </LinearLayout>


            <LinearLayout
                android:id="@+id/custom_add_terms_ll1"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_gravity="center_horizontal"
                android:orientation="horizontal"
                android:paddingLeft="20dp"
                android:paddingRight="20dp"
                android:visibility="gone"
                tools:visibility="visible">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/terms_and"
                    android:textColor="@color/text_color_black_nomal"
                    android:textSize="12sp" />

                <TextView
                    android:id="@+id/custom_add_terms_2"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="@string/terms_disclaimer"
                    android:textColor="@color/color_main"
                    android:textSize="12sp" />

            </LinearLayout>

        </LinearLayout>

        <ImageView
            android:id="@+id/custom_middle_cancel_iv"
            android:layout_width="42dp"
            android:layout_height="42dp"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="20dp"
            android:src="@drawable/icon_dialog_close"
            android:visibility="gone" />

    </LinearLayout>
</ScrollView>