# 极光推送厂商通道集成文档

## 概述

本项目已成功集成极光推送5.7.0版本的厂商通道，支持以下推送通道：

- **FCM (Firebase Cloud Messaging)** - 海外推送服务
- **小米推送** - 小米设备专用推送
- **华为推送** - 华为设备专用推送
- **极光推送** - 基础推送服务（兜底方案）

## 集成内容

### 1. 依赖配置

在 `totwoo/build.gradle` 中已添加：

```gradle
// 极光推送厂商通道插件
implementation 'cn.jiguang.sdk.plugin:fcm:5.7.0'        // FCM 通道
implementation 'cn.jiguang.sdk.plugin:mi:5.7.0'         // 小米通道
implementation 'cn.jiguang.sdk.plugin:huawei:5.7.0'     // 华为通道
```

### 2. 配置文件

- `google-services.json` - FCM推送配置（已存在）
- `agconnect-services.json` - 华为推送配置（已存在）
- 小米推送配置在代码中硬编码

### 3. 权限配置

在 `AndroidManifest.xml` 中已添加：

```xml
<!-- 华为推送权限 -->
<permission android:name="com.totwoo.totwoo.permission.PROCESS_PUSH_MSG" 
           android:protectionLevel="signatureOrSystem" />
<uses-permission android:name="com.huawei.android.launcher.permission.CHANGE_BADGE" />

<!-- 小米推送权限 -->
<permission android:name="com.totwoo.totwoo.permission.MIPUSH_RECEIVE" 
           android:protectionLevel="signature" />
```

### 4. 服务配置

已在 `AndroidManifest.xml` 中配置：

- `FCMMessageService` - FCM消息服务
- `HuaweiMessageService` - 华为推送服务
- `MiPushReceiver` - 小米推送接收器

## 代码结构

### 核心类

1. **VendorPushConfig.java** - 厂商通道配置和初始化
2. **FCMMessageService.java** - FCM消息处理服务
3. **HuaweiMessageService.java** - 华为推送消息处理服务
4. **MiPushReceiver.java** - 小米推送消息接收器
5. **PushTestUtils.java** - 推送功能测试工具

### 初始化流程

在 `ToTwooApplication.java` 中：

```java
// 初始化极光推送
JPushInterface.init(ToTwooApplication.baseContext);

// 初始化厂商推送通道
VendorPushConfig.initVendorPush(this);
```

## 推送通道优先级

1. **厂商通道**（华为/小米等）- 系统级推送，到达率最高
2. **FCM通道** - 海外推送，需要Google服务
3. **极光推送** - 基础推送通道，兜底方案

## 测试功能

在调试页面（DebugActivity）中提供了推送测试功能：

- 查看当前设备支持的推送通道
- 检查推送服务状态
- 测试推送功能
- 显示推送通道优先级说明

## 配置说明

### 小米推送配置

当前使用的配置（来自tim模块）：
- AppID: `2882303761517409136`
- AppKey: `5331740911136`

### 华为推送配置

依赖 `agconnect-services.json` 文件中的配置。

### FCM配置

依赖 `google-services.json` 文件中的配置。

## 工作原理

1. **自动检测设备类型** - 根据设备厂商自动选择对应的推送通道
2. **消息转发机制** - 厂商推送消息统一转发给极光推送处理
3. **兜底机制** - 厂商通道不可用时自动使用极光推送

## 注意事项

1. **小米设备** - 只在小米、红米、POCO设备上初始化小米推送
2. **华为设备** - 只在华为、荣耀设备上初始化华为推送
3. **FCM通道** - 在所有设备上都会初始化，但需要Google服务支持
4. **权限要求** - 确保应用有通知权限

## 验证方法

1. 在调试页面点击"测试推送通道"按钮
2. 查看日志输出，确认各通道初始化状态
3. 发送测试推送消息验证到达率

## 故障排除

1. **推送不到达** - 检查设备通知权限和推送服务状态
2. **厂商通道失败** - 查看对应厂商推送服务是否正常
3. **FCM失败** - 检查Google服务是否可用
4. **配置错误** - 验证配置文件是否正确放置

## 更新日志

- 2024-08-18: 完成FCM、小米、华为推送通道集成
- 集成极光推送5.7.0版本
- 添加推送测试功能
- 完善错误处理和日志记录
